# Gait Parameter Calibration Integration Guide

This guide explains how to integrate the calibration system with the main API response processing in `main.py`.

## Overview

The calibration system uses trained ML models to improve gait parameter predictions by leveraging measured velocity from distance sensors. When a measured velocity is available, the system applies intelligent corrections to the API response parameters.

## Files Created

1. **`api_calibration.py`** - Main calibration module
2. **`calibration_integration_demo.py`** - Demonstration and testing
3. **`main_with_calibration_example.py`** - Integration example
4. **`CALIBRATION_INTEGRATION_GUIDE.md`** - This guide

## Key Features

### ✅ Automatic Parameter Mapping
- Handles naming differences between API response and calibration system
- Maps `D_Supp_Perc_L/R` to `D_Supp_PercL/R` automatically

### ✅ Intelligent Calibration
- Uses velocity-based correction weights
- Applies stronger corrections when velocity is outside model training range
- Maintains physical constraints (e.g., Swing + Stance = 100%)

### ✅ Flexible Integration
- Works with or without measured velocity
- Can be enabled/disabled via UI controls
- Preserves original API response structure

### ✅ Robust Error Handling
- Falls back to original response if calibration fails
- Provides detailed logging and error messages

## Quick Integration Steps

### 1. Install Dependencies
```bash
pip install numpy pandas matplotlib seaborn scikit-learn openpyxl flaml xgboost lightgbm
```

### 2. Add Import to main.py
```python
from api_calibration import integrate_calibration_with_main
```

### 3. Add UI Controls (Sidebar)
```python
with st.sidebar:
    # Existing controls...
    target_distance = st.slider('target_distance', 1.0, 5.0, 3.0, step=0.1)
    
    # Add calibration controls
    st.header("Calibration Settings")
    enable_calibration = st.checkbox("Enable Calibration", value=True)
    measured_velocity = st.number_input(
        "Measured Velocity (cm/s)", 
        min_value=0.0, 
        max_value=200.0, 
        value=None,
        help="Velocity from distance sensor (leave empty if not available)"
    )
```

### 4. Modify API Response Processing
Replace the API call sections in all tabs:

```python
# Original code:
res = invoke_api(video_file)

# Modified code:
res = invoke_api(video_file)
calibrated_res = integrate_calibration_with_main(
    api_response_json=res,
    measured_velocity=measured_velocity if enable_calibration and measured_velocity else None,
    enable_calibration=enable_calibration
)

# Continue with existing processing using calibrated_res
y_pred = pd.read_json(calibrated_res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns]
```

## Calibration Parameters

The system calibrates these parameters:
- `Stride_Len_L`, `Stride_Len_R` - Stride lengths
- `Swing_Perc_L`, `Swing_Perc_R` - Swing percentages  
- `Stance_Perc_L`, `Stance_Perc_R` - Stance percentages
- `D_Supp_Perc_L`, `D_Supp_Perc_R` - Double support percentages

Other parameters in the API response remain unchanged.

## Usage Examples

### Basic Usage
```python
from api_calibration import integrate_calibration_with_main

# With measured velocity
calibrated_response = integrate_calibration_with_main(
    api_response_json=original_response,
    measured_velocity=95.0,  # cm/s from distance sensor
    enable_calibration=True
)

# Without measured velocity (no calibration applied)
response = integrate_calibration_with_main(
    api_response_json=original_response,
    measured_velocity=None,
    enable_calibration=True
)
```

### Advanced Usage
```python
from api_calibration import APIResponseCalibrator

calibrator = APIResponseCalibrator()
calibrated_response = calibrator.calibrate_api_response(
    api_response=api_response_dict,
    measured_velocity=95.0,
    calibration_weight=0.7,  # Stronger calibration
    model_velocity_range=(30.0, 150.0),
    use_velocity_constraint=True,
    velocity_tolerance=8.0
)
```

## Testing

Run the demonstration to verify everything works:

```bash
python calibration_integration_demo.py
```

Expected output:
- ✅ Calibration system loads successfully
- 📊 Shows before/after parameter comparisons
- 🔧 Demonstrates different calibration scenarios

## Calibration Logic

1. **Velocity Assessment**: Determines how far the measured velocity is from the model's training range
2. **Correction Weight Calculation**: Higher weights for velocities outside the training range
3. **ML-Based Correction**: Uses trained models to predict expected parameters for the measured velocity
4. **Weighted Blending**: Combines original predictions with ML corrections
5. **Physical Constraints**: Ensures results satisfy biomechanical constraints
6. **Consistency Checks**: Validates parameter relationships (e.g., stride length vs velocity)

## Troubleshooting

### Import Errors
- Ensure all dependencies are installed
- Check that `param_calibration/models/` contains trained models

### Calibration Not Applied
- Verify `measured_velocity` is provided and > 0
- Check that `enable_calibration=True`
- Look for error messages in console output

### Model Loading Issues
- Run `python param_calibration/run_calibration.py` to retrain models if needed
- Check model file permissions and paths

## Performance Impact

- **Minimal**: Calibration adds ~50-100ms per API response
- **Memory**: ~10MB additional memory for loaded models
- **CPU**: Light computational overhead for parameter correction

## Benefits

1. **Improved Accuracy**: Leverages precise velocity measurements to correct other parameters
2. **Robustness**: Handles edge cases where vision-based predictions may be less reliable
3. **Consistency**: Ensures parameter relationships follow biomechanical principles
4. **Flexibility**: Can be easily enabled/disabled based on sensor availability

## Next Steps

1. Integrate the calibration system into your main.py
2. Add distance sensor integration to provide measured velocity
3. Test with real gait analysis data
4. Monitor calibration effectiveness and adjust parameters as needed
5. Consider expanding calibration to additional parameters

For questions or issues, refer to the demonstration files or check the calibration system logs.

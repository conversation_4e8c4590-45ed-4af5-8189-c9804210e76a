"""
API Response Calibration Module

This module provides functionality to apply calibration to gait parameter predictions
from the API response using the postprocessing system in param_calibration/calibration_utils.py.
"""

import json
import pandas as pd
import sys
import os
from typing import Dict, Any, Optional

# Add param_calibration to Python path to resolve import issues
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'param_calibration'))

from param_calibration.calibration_utils import GaitCalibrationPredictor


class APIResponseCalibrator:
    """
    Calibrates API response gait parameters using the trained ML calibration models.
    """
    
    def __init__(self, models_dir='./param_calibration/models'):
        """
        Initialize the calibrator with the trained models.
        
        Args:
            models_dir (str): Directory containing the trained calibration models
        """
        self.predictor = GaitCalibrationPredictor(models_dir=models_dir)
        
        # Mapping from API response parameter names to calibration system names
        self.parameter_mapping = {
            'Stride_Len_L': 'Stride_Len_L',
            'Stride_Len_R': 'Stride_Len_R', 
            'Swing_Perc_L': 'Swing_Perc_L',
            'Swing_Perc_R': 'Swing_Perc_R',
            'Stance_Perc_L': 'Stance_Perc_L',
            'Stance_Perc_R': 'Stance_Perc_R',
            'D_Supp_Perc_L': 'D_Supp_PercL',  # Note the naming difference
            'D_Supp_Perc_R': 'D_Supp_PercR',  # Note the naming difference
        }
        
        print(f"✅ API Response Calibrator initialized")
        print(f"   Calibration target parameters: {self.predictor.target_columns}")
        print(f"   Parameter mapping: {self.parameter_mapping}")
    
    def calibrate_api_response(self, 
                             api_response: Dict[str, Any], 
                             measured_velocity: float,
                             calibration_weight: float = 0.5,
                             model_velocity_range: tuple = (30.0, 150.0),
                             use_velocity_constraint: bool = True,
                             velocity_tolerance: float = 10.0) -> Dict[str, Any]:
        """
        Apply calibration to the API response gait parameters.
        
        Args:
            api_response (Dict): The original API response JSON
            measured_velocity (float): Measured velocity from distance sensor (cm/s)
            calibration_weight (float): Weight for calibration (0.0 = no calibration, 1.0 = full calibration)
            model_velocity_range (tuple): Velocity range the model was trained on
            use_velocity_constraint (bool): Whether to apply velocity constraints
            velocity_tolerance (float): Tolerance for velocity constraints
            
        Returns:
            Dict: Calibrated API response with same structure as input
        """
        # Extract gait parameters from API response
        if 'gait_parameters' not in api_response:
            raise ValueError("API response must contain 'gait_parameters' field")
        
        gait_params = api_response['gait_parameters'].copy()
        
        # Map API parameter names to calibration system names
        mapped_params = {}
        for api_name, calib_name in self.parameter_mapping.items():
            if api_name in gait_params:
                mapped_params[calib_name] = gait_params[api_name]
        
        print(f"🔧 Applying calibration to {len(mapped_params)} parameters")
        print(f"   Measured velocity: {measured_velocity} cm/s")
        print(f"   Calibration weight: {calibration_weight}")
        
        # Apply postprocessing calibration
        calibrated_params = self.predictor.postprocess_model_output(
            model_predictions=mapped_params,
            measured_velocity=measured_velocity,
            model_velocity_range=model_velocity_range,
            use_velocity_constraint=use_velocity_constraint,
            velocity_tolerance=velocity_tolerance
        )
        
        # Remove metadata from calibrated results
        metadata_keys = [key for key in calibrated_params.keys() if key.startswith('_')]
        correction_info = calibrated_params.pop('_correction_info', {})
        for key in metadata_keys:
            calibrated_params.pop(key, None)
        
        # Map calibrated parameters back to API response format
        calibrated_gait_params = gait_params.copy()  # Start with original parameters
        
        # Update with calibrated values
        reverse_mapping = {v: k for k, v in self.parameter_mapping.items()}
        for calib_name, calibrated_value in calibrated_params.items():
            if calib_name in reverse_mapping:
                api_name = reverse_mapping[calib_name]
                calibrated_gait_params[api_name] = calibrated_value
            elif calib_name == 'Velocity':
                calibrated_gait_params['Velocity'] = calibrated_value
        
        # Create calibrated API response
        calibrated_response = api_response.copy()
        calibrated_response['gait_parameters'] = calibrated_gait_params
        
        # Add calibration metadata
        calibrated_response['calibration_info'] = {
            'calibrated': True,
            'measured_velocity': measured_velocity,
            'calibration_weight': calibration_weight,
            'calibrated_parameters': list(mapped_params.keys()),
            'corrections_applied': sum(1 for info in correction_info.values() 
                                     if info.get('correction_applied', False)),
            'total_parameters_processed': len(correction_info)
        }
        
        # Log calibration summary
        corrections_applied = calibrated_response['calibration_info']['corrections_applied']
        total_processed = calibrated_response['calibration_info']['total_parameters_processed']
        print(f"✅ Calibration completed: {corrections_applied}/{total_processed} parameters corrected")
        
        return calibrated_response
    
    def calibrate_api_response_simple(self, 
                                    api_response: Dict[str, Any], 
                                    measured_velocity: float) -> Dict[str, Any]:
        """
        Simplified calibration with default parameters.
        
        Args:
            api_response (Dict): The original API response JSON
            measured_velocity (float): Measured velocity from distance sensor (cm/s)
            
        Returns:
            Dict: Calibrated API response
        """
        return self.calibrate_api_response(
            api_response=api_response,
            measured_velocity=measured_velocity,
            calibration_weight=0.5,  # Moderate calibration
            model_velocity_range=(54.0, 93.9),
            use_velocity_constraint=True,
            velocity_tolerance=10.0
        )


def calibrate_api_response_json(api_response_json: str,
                              measured_velocity: float,
                              models_dir: str = './param_calibration/models') -> str:
    """
    Convenience function to calibrate API response from JSON string.

    Args:
        api_response_json (str): API response as JSON string
        measured_velocity (float): Measured velocity from distance sensor (cm/s)
        models_dir (str): Directory containing calibration models

    Returns:
        str: Calibrated API response as JSON string
    """
    # Parse JSON
    api_response = json.loads(api_response_json)

    # Initialize calibrator
    calibrator = APIResponseCalibrator(models_dir=models_dir)

    # Apply calibration
    calibrated_response = calibrator.calibrate_api_response_simple(
        api_response=api_response,
        measured_velocity=measured_velocity
    )

    # Return as JSON string
    return json.dumps(calibrated_response, indent=2)


def integrate_calibration_with_main(api_response_json: str,
                                  measured_velocity: Optional[float] = None,
                                  enable_calibration: bool = True) -> str:
    """
    Integration function for main.py to apply calibration to API responses.

    This function can be easily integrated into the existing main.py workflow
    to apply calibration when a measured velocity is available.

    Args:
        api_response_json (str): Original API response as JSON string
        measured_velocity (Optional[float]): Measured velocity from distance sensor (cm/s)
                                           If None, no calibration is applied
        enable_calibration (bool): Whether to enable calibration

    Returns:
        str: Calibrated API response as JSON string (or original if no calibration)
    """
    # If calibration is disabled or no measured velocity, return original
    if not enable_calibration or measured_velocity is None:
        print("ℹ️  Calibration skipped (disabled or no measured velocity)")
        return api_response_json

    try:
        # Apply calibration
        print(f"🔧 Applying calibration with measured velocity: {measured_velocity} cm/s")
        calibrated_json = calibrate_api_response_json(
            api_response_json=api_response_json,
            measured_velocity=measured_velocity
        )
        print("✅ Calibration applied successfully")
        return calibrated_json

    except Exception as e:
        print(f"⚠️  Calibration failed: {e}")
        print("   Returning original API response")
        return api_response_json


# Example usage and testing
if __name__ == "__main__":
    # Example API response
    example_api_response = {
        "gait_parameters": {
            "Velocity": 100.3327,
            "Cadence": 101.2393,
            "Cycle_Time_L": 1.1798,
            "Cycle_Time_R": 1.1814,
            "Stride_Len_L": 118.9,
            "Stride_Len_R": 118.9281,
            "Supp_Base_L": 9.3145,
            "Supp_Base_R": 9.2847,
            "Swing_Perc_L": 38.8685,
            "Swing_Perc_R": 38.8074,
            "Stance_Perc_L": 61.134,
            "Stance_Perc_R": 61.1955,
            "D_Supp_Perc_L": 22.5706,
            "D_Supp_Perc_R": 22.5472,
            "ToeInOut_L": 2.6259,
            "ToeInOut_R": 7.5333,
            "StrideLen_CV_L": 8.9616,
            "StrideLen_CV_R": 7.7936,
            "StrideTm_CV_L": 49.9642,
            "StrideTm_CV_R": 46.44
        },
        "gait_score": 9.298256544875823,
        "status_code": 200,
        "message": "success"
    }
    
    # Test calibration
    try:
        calibrator = APIResponseCalibrator()
        
        # Apply calibration with measured velocity
        measured_velocity = 95.0  # cm/s from distance sensor
        
        calibrated_response = calibrator.calibrate_api_response_simple(
            api_response=example_api_response,
            measured_velocity=measured_velocity
        )
        
        print("\n" + "="*60)
        print("CALIBRATION EXAMPLE")
        print("="*60)
        
        print("\n📊 Original vs Calibrated Parameters:")
        original_params = example_api_response['gait_parameters']
        calibrated_params = calibrated_response['gait_parameters']
        
        for param in calibrator.parameter_mapping.keys():
            if param in original_params and param in calibrated_params:
                orig_val = original_params[param]
                calib_val = calibrated_params[param]
                diff = calib_val - orig_val
                print(f"  {param:15s}: {orig_val:8.3f} → {calib_val:8.3f} (Δ={diff:+7.3f})")
        
        print(f"\n📈 Calibration Summary:")
        calib_info = calibrated_response['calibration_info']
        print(f"  Measured velocity: {calib_info['measured_velocity']} cm/s")
        print(f"  Corrections applied: {calib_info['corrections_applied']}/{calib_info['total_parameters_processed']}")
        print(f"  Calibrated parameters: {calib_info['calibrated_parameters']}")
        
    except Exception as e:
        print(f"❌ Error during calibration test: {e}")
        print("Make sure the calibration models are trained and available in param_calibration/models/")

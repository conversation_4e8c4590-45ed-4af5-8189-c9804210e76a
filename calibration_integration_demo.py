"""
Demonstration of API Response Calibration Integration

This script shows how to integrate the calibration system with the main.py workflow.
It demonstrates both the calibration functionality and how to modify the existing code.
"""

import json
import pandas as pd
from api_calibration import APIResponseCalibrator, integrate_calibration_with_main


def demo_calibration_integration():
    """
    Demonstrate the calibration integration with example API responses.
    """
    print("🎯 API Response Calibration Integration Demo")
    print("=" * 60)
    
    # Example API response (same structure as in main.py)
    example_api_response = {
        "gait_parameters": {
            "Velocity": 100.3327,
            "Cadence": 101.2393,
            "Cycle_Time_L": 1.1798,
            "Cycle_Time_R": 1.1814,
            "Stride_Len_L": 118.9,
            "Stride_Len_R": 118.9281,
            "Supp_Base_L": 9.3145,
            "Supp_Base_R": 9.2847,
            "Swing_Perc_L": 38.8685,
            "Swing_Perc_R": 38.8074,
            "Stance_Perc_L": 61.134,
            "Stance_Perc_R": 61.1955,
            "D_Supp_Perc_L": 22.5706,
            "D_Supp_Perc_R": 22.5472,
            "ToeInOut_L": 2.6259,
            "ToeInOut_R": 7.5333,
            "StrideLen_CV_L": 8.9616,
            "StrideLen_CV_R": 7.7936,
            "StrideTm_CV_L": 49.9642,
            "StrideTm_CV_R": 46.44
        },
        "gait_score": 9.298256544875823,
        "status_code": 200,
        "message": "success"
    }
    
    # Convert to JSON string (as it would come from the API)
    api_response_json = json.dumps(example_api_response)
    
    print("\n📊 Original API Response:")
    print(f"  Velocity: {example_api_response['gait_parameters']['Velocity']:.3f} cm/s")
    print(f"  Stride_Len_L: {example_api_response['gait_parameters']['Stride_Len_L']:.3f} cm")
    print(f"  Stride_Len_R: {example_api_response['gait_parameters']['Stride_Len_R']:.3f} cm")
    print(f"  Swing_Perc_L: {example_api_response['gait_parameters']['Swing_Perc_L']:.3f} %")
    print(f"  Swing_Perc_R: {example_api_response['gait_parameters']['Swing_Perc_R']:.3f} %")
    
    # Scenario 1: Normal calibration with measured velocity
    print("\n" + "="*60)
    print("SCENARIO 1: Normal Calibration")
    print("="*60)
    
    measured_velocity = 95.0  # cm/s from distance sensor
    calibrated_json = integrate_calibration_with_main(
        api_response_json=api_response_json,
        measured_velocity=measured_velocity,
        enable_calibration=True
    )
    
    calibrated_response = json.loads(calibrated_json)
    
    print("\n📈 Calibrated Results:")
    calibrated_params = calibrated_response['gait_parameters']
    print(f"  Velocity: {calibrated_params['Velocity']:.3f} cm/s")
    print(f"  Stride_Len_L: {calibrated_params['Stride_Len_L']:.3f} cm")
    print(f"  Stride_Len_R: {calibrated_params['Stride_Len_R']:.3f} cm")
    print(f"  Swing_Perc_L: {calibrated_params['Swing_Perc_L']:.3f} %")
    print(f"  Swing_Perc_R: {calibrated_params['Swing_Perc_R']:.3f} %")
    
    if 'calibration_info' in calibrated_response:
        calib_info = calibrated_response['calibration_info']
        print(f"\n🔧 Calibration Info:")
        print(f"  Measured velocity: {calib_info['measured_velocity']} cm/s")
        print(f"  Corrections applied: {calib_info['corrections_applied']}/{calib_info['total_parameters_processed']}")
    
    # Scenario 2: No calibration (no measured velocity)
    print("\n" + "="*60)
    print("SCENARIO 2: No Calibration (No Measured Velocity)")
    print("="*60)
    
    no_calib_json = integrate_calibration_with_main(
        api_response_json=api_response_json,
        measured_velocity=None,  # No measured velocity
        enable_calibration=True
    )
    
    print("✅ Original response returned unchanged")
    
    # Scenario 3: Calibration disabled
    print("\n" + "="*60)
    print("SCENARIO 3: Calibration Disabled")
    print("="*60)
    
    disabled_json = integrate_calibration_with_main(
        api_response_json=api_response_json,
        measured_velocity=95.0,
        enable_calibration=False  # Calibration disabled
    )
    
    print("✅ Original response returned unchanged")
    
    return calibrated_response


def show_integration_code_example():
    """
    Show how to integrate calibration into main.py
    """
    print("\n" + "="*60)
    print("INTEGRATION CODE EXAMPLE FOR main.py")
    print("="*60)
    
    integration_code = '''
# Add this import at the top of main.py
from api_calibration import integrate_calibration_with_main

# In the invoke_api function or where API response is processed:
def process_api_response_with_calibration(video_file, measured_velocity=None):
    """
    Process API response with optional calibration
    
    Args:
        video_file: Video file to analyze
        measured_velocity: Optional measured velocity from distance sensor (cm/s)
    """
    # Original API call
    res = invoke_api(video_file)
    
    # Apply calibration if measured velocity is available
    calibrated_res = integrate_calibration_with_main(
        api_response_json=res,
        measured_velocity=measured_velocity,
        enable_calibration=True  # Can be controlled by UI toggle
    )
    
    # Continue with existing processing
    y_pred = pd.read_json(calibrated_res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns]
    
    return y_pred, calibrated_res

# Example usage in tab2 (sample test):
if invoke_btn:
    video_bytes = open(video_path, 'rb').read()
    new_video_bytes = write_video_from_bytes(video_bytes, "output.mp4", add_noise=False, trim_video=enable_trim_video)
    
    # Get measured velocity from distance sensor (example)
    measured_velocity = 95.0  # This would come from your distance sensor
    
    with st.spinner('Analyzing gait video...'):
        # Use the new function with calibration
        y_pred, calibrated_res = process_api_response_with_calibration(
            video_file=new_video_bytes,
            measured_velocity=measured_velocity
        )
    
    # Rest of the processing remains the same...
'''
    
    print(integration_code)


def demonstrate_parameter_mapping():
    """
    Show the parameter mapping between API response and calibration system
    """
    print("\n" + "="*60)
    print("PARAMETER MAPPING")
    print("="*60)
    
    calibrator = APIResponseCalibrator()
    
    print("\n📋 API Response → Calibration System Mapping:")
    for api_name, calib_name in calibrator.parameter_mapping.items():
        status = "✅" if api_name == calib_name else "🔄"
        print(f"  {status} {api_name:15s} → {calib_name}")
    
    print(f"\n📊 Calibration System Target Parameters:")
    for i, param in enumerate(calibrator.predictor.target_columns, 1):
        print(f"  {i:2d}. {param}")
    
    print(f"\n⚠️  Note: Parameters not in the mapping will remain unchanged in the API response")


if __name__ == "__main__":
    try:
        # Run the demonstration
        demo_calibration_integration()
        
        # Show integration code example
        show_integration_code_example()
        
        # Show parameter mapping
        demonstrate_parameter_mapping()
        
        print("\n" + "="*60)
        print("✅ DEMO COMPLETED SUCCESSFULLY")
        print("="*60)
        print("\nNext steps:")
        print("1. Review the integration code example above")
        print("2. Add the calibration import to main.py")
        print("3. Modify the API response processing to use calibration")
        print("4. Add UI controls for measured velocity input")
        print("5. Test with real API responses")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        print("\nMake sure:")
        print("1. Calibration models are available in param_calibration/models/")
        print("2. All required dependencies are installed")
        print("3. Run 'python param_calibration/run_calibration.py' if models are missing")

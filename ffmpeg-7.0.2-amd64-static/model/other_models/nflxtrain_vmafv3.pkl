(dp0
S'param_dict'
p1
(dp2
S'norm_type'
p3
S'clip_0to1'
p4
sS'score_clip'
p5
(lp6
F0.0
aF100.0
asS'C'
p7
F4.0
sS'nu'
p8
F0.9
sS'gamma'
p9
F0.05
ssS'model_dict'
p10
(dp11
S'feature_dict'
p12
(dp13
S'VMAF_feature'
p14
(lp15
S'vif_scale0'
p16
aS'vif_scale1'
p17
aS'vif_scale2'
p18
aS'vif_scale3'
p19
aS'adm2'
p20
aS'motion'
p21
assg3
S'linear_rescale'
p22
sg5
g6
sS'feature_names'
p23
(lp24
S'VMAF_feature_adm2_score'
p25
aS'VMAF_feature_motion_score'
p26
aS'VMAF_feature_vif_scale0_score'
p27
aS'VMAF_feature_vif_scale1_score'
p28
aS'VMAF_feature_vif_scale2_score'
p29
aS'VMAF_feature_vif_scale3_score'
p30
asS'intercepts'
p31
(lp32
F-0.1909090909090909
aF-1.9715194224195431
aF-0.022214587359292954
aF-0.08217305517359848
aF-0.2767600362748038
aF-0.469070289400241
aF-0.7460857020871375
asS'model_type'
p33
S'LIBSVMNUSVR'
p34
sS'model'
p35
NsS'slopes'
p36
(lp37
F0.010909090909090908
aF2.971519422419543
aF0.06846153126171134
aF1.0821730334476833
aF1.2767601181740016
aF1.4690704623045836
aF1.746086042520506
ass.
#%%
import shutil
import warnings

from matplotlib import pyplot as plt

from trim_video import trim_center_segment
warnings.filterwarnings('ignore')

#%%
import json
import os
import string
import subprocess
import numpy as np
import pandas as pd
import requests
import streamlit as st
from pathlib import Path
from sklearn.metrics import mean_absolute_percentage_error, r2_score
from stqdm import stqdm
from scipy.stats import zscore
from natsort import natsorted
import tempfile
from moviepy.editor import VideoFileClip

# Import calibration system
from api_calibration import integrate_calibration_with_main

st.set_page_config(page_title='게이트 스캐너 성능 평가', page_icon='🧪', layout='wide')
st.markdown("""
        <style>
               .block-container {
                    padding-top: 1.5rem;
                    padding-bottom: 0rem;
                    padding-left: 5rem;
                    padding-right: 5rem;
                }
        </style>
        """, unsafe_allow_html=True)

target_columns = [
    'Velocity',
    # 'Cadence',
    # 'Cycle_Time_L', 'Cycle_Time_R',
    'Stride_Len_L', 'Stride_Len_R',
    'Supp_Base_L', 'Supp_Base_R',
    'Swing_Perc_L', 'Swing_Perc_R',
    'Stance_Perc_L', 'Stance_Perc_R',
    'D_Supp_PercL', 'D_Supp_PercR',
    # 'ToeInOutL', 'ToeInOutR',
    # 'StrideLen_CV_L', 'StrideLen_CV_R',
    # 'StrideTm_CV_L', 'StrideTm_CV_R'
]

eval_columns = [col for col in target_columns if col != 'Velocity']

def get_evaluation_columns(enable_calibration):
    """
    Get columns for performance evaluation, excluding Velocity when calibration is enabled.

    Args:
        enable_calibration (bool): Whether calibration is enabled

    Returns:
        list: Columns to use for evaluation metrics
    """
    if enable_calibration:
        # Exclude Velocity from evaluation when calibration is enabled
        # because calibration uses ground truth velocity directly
        return [col for col in target_columns if col != 'Velocity']
    else:
        return target_columns

rename_map = {
            'D_Supp_Perc_L': 'D_Supp_PercL',
            'D_Supp_Perc_R': 'D_Supp_PercR',
            'ToeInOut_L': 'ToeInOutL',
            'ToeInOut_R': 'ToeInOutR',
}

# 전역 변수
dataset_file = '/datasets/gait/SingleView/gaitrite_full_dataset.xlsx'
test_file_path = './test_vids.txt'
# video_root = '/data/hossay/GAITRite-dataset/v2/video_formated_trim'
video_root = '/datasets/gait/SingleView/videos/video_formated_trim'

# TODO: 서버에 따라 성능 차이 존재함!! 성능 확인 필요!
# url = "http://aicu-office.iptime.org:65004/api/v1/gait/"
# url = "http://**************:5002/api/v1/gait"
url = "http://************:8004/api/v1/gait"

n_subjects = 'all'
# n_subjects = 100
trials = 4
enable_trim_video = False

with st.sidebar:
    target_distance = st.slider('target_distance', 1.0,  5.0, 3.0, step=0.1)     # meter

    # Calibration controls
    st.header("🔧 Calibration Settings")
    enable_calibration = st.checkbox("Enable ML Calibration", value=True,
                                   help="Use ground truth velocity to calibrate API predictions")

    if enable_calibration:
        st.success("✅ Calibration enabled - Using ground truth velocity from GAITRite dataset")
    else:
        st.info("ℹ️ Calibration disabled - Using original API predictions")

if n_subjects == 'all':
    sample_size = None
else:
    sample_size  = n_subjects * trials # None : 'full evaluation'

#%%

def get_test_vids(test_file_path, trials=4):
    test_vids = [ line.strip() for line in open(test_file_path).readlines() ]
    patient_ids = pd.Series(test_vids).str.split('_').str[0]
    test_vids = pd.Series(test_vids)

    test_vids_filtered = []
    trial_counts = {}
    for pid in patient_ids.unique():
        pid_idxs = test_vids[test_vids.str.startswith(pid)].index.tolist()
        n_trials = len(pid_idxs)
        if n_trials == trials:
            trial_counts[pid] = n_trials
            test_vids_filtered += test_vids[test_vids.str.startswith(pid)].sort_values(ascending=True).tolist()

    return test_vids_filtered


# 이상치를 필터링하는 함수 정의
def filter_outliers_zscore(df, columns, threshold=1):
    filtered_df = df.copy()
    for col in columns:
        z_scores = zscore(df[col])  # z-score 계산
        filtered_df = filtered_df[abs(z_scores) <= threshold]
    return filtered_df


@st.cache_data()
def load_dataset():
    # load total dataset df
    df = pd.read_excel(dataset_file)

    # test_vids = get_test_vids(test_file_path, trials=trials)[:sample_size]
    test_vids = [ line.strip() for line in open(test_file_path).readlines() ]

    df['Time'] = pd.to_datetime(df['Time'])
    # vfiles = df.query(f"'2021-10-15' <= Time")['VideoFile'].str.replace('/data/GAITRite-dataset/v2/video_formated_trim/', '').str.replace('.avi', '').tolist()
    vfiles = df['VideoFile'].str.replace('/data/GAITRite-dataset/v2/video_formated_trim/', '').str.replace('.avi', '').tolist()
    test_vids = list(set(vfiles).intersection(set(test_vids)))[:sample_size]

    test_idxs = df['VideoFile'].str.split('/').str[-1].str.replace('.avi', '').isin(test_vids)
    df_test = df.loc[test_idxs]
    df_test.index = df_test['VideoFile'].str.split('/').str[-1].str.replace('.avi', '')
    df_test.index.name = 'Case_ID'
    df_test = df_test.loc[:, target_columns]
    df_test = df_test.sort_values('Case_ID', ascending=True)

    # 이상치 제거
    df_test = filter_outliers_zscore(df_test, columns=target_columns, threshold=1)

    return df_test

def invoke_api(video_file):
    if isinstance(video_file, str):
        video_file = open(video_file, 'rb')

    payload = {}
    files=[
        ('video_file',('filename', video_file, 'application/octet-stream')),
    ]
    headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJob3NzYXkiLCJleHAiOjE3ODkwMjA1MTEsInR5cGUiOiJhY2Nlc3MifQ.Ant7sKKrywI2EKv0tp4kc3mYqME0B_qKuKKBpERBYNs'
    }

    response = requests.request("POST", url, headers=headers, data=payload, files=files)

    return json.dumps(response.json())

def compute_mape(row):
    mape = mean_absolute_percentage_error(row[['정답값']], row[['예측값']])
    return mape * 100

import cv2
import numpy as np
from pathlib import Path

import cv2
import numpy as np
from pathlib import Path

def add_noise_to_video(input_path, kernel_size=(5, 5)):
    """
    비디오의 각 프레임에 블러를 추가하고, 같은 이름의 avi 파일로 저장합니다.
    
    Args:
        input_path (str): 원본 비디오 경로.
        kernel_size (tuple): 가우시안 블러의 커널 크기 (홀수로 설정 필요).
    """
    # 파일 경로 처리
    input_path = Path(input_path)
    output_path = input_path.with_stem("proc")

    # 비디오 캡처 초기화
    cap = cv2.VideoCapture(str(input_path))
    
    # 비디오 속성 가져오기
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fps = cap.get(cv2.CAP_PROP_FPS)

    # 비디오 작성기 초기화
    fourcc = cv2.VideoWriter_fourcc(*'XVID')  # AVI 코덱
    out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

    # 프레임 처리
    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # # 블러 효과 추가 (가우시안 블러)
        # blurred_frame = cv2.GaussianBlur(frame, kernel_size, 0)

        # 결과를 파일로 저장
        out.write(frame)

    # 자원 해제
    cap.release()
    out.release()
    print(f"Blurred video saved to {output_path}")

def report_performance(df_all_results, enable_calibration=False):
    # 성능 통계 테이블
    if enable_calibration:
        st.markdown('##### 1. 보행 파라미터별 성능 통계 (보행 분석 오차율) - Velocity 제외')
        st.info("ℹ️ Velocity는 보정 시스템에서 정답값을 직접 사용하므로 평가에서 제외됩니다.")
    else:
        st.markdown('##### 1. 보행 파라미터별 성능 통계 (보행 분석 오차율)')

    # ID별 평균 정답값 및 예측값 계산
    df_all_results_agg = df_all_results.groupby(['ID', 'index']).agg({
        '정답값': 'mean',
        '예측값': 'mean',
    })

    # 각 보행 변수별 분석 오차율 재계산
    df_all_results_agg['분석 오차율 (%)'] = df_all_results_agg.apply(
        lambda row: compute_mape(row), axis=1
    )

    # 각 보행 변수별 통계 계산 (evaluation columns만 사용)
    param_stats = df_all_results_agg.groupby('index')['분석 오차율 (%)'].describe().reindex(index=eval_columns).T

    # 마이크로 평균 계산 (전체 샘플에 대한 분석 오차율를 계산)
    micro_avg = df_all_results_agg['분석 오차율 (%)'].mean()
    max_val_avg = param_stats.loc['max'].mean()

    # 성능 통계 출력 (보행 파라미터별 성능과 전체 성능)
    st.dataframe(param_stats)
    st.markdown("##### 2. 보행 파라미터 평균 오차율 (마이크로 평균 기반)")
    st.markdown(f'<h4 style="color: blue; background-color: orange; width:30%;">평균 분석 오차율: {micro_avg:.2f}%</h4>', unsafe_allow_html=True)

    st.markdown("##### 3. 보행 파라미터 최대 오차율 (최대 오차율 평균)")
    st.markdown(f'<h4 style="color: yellow; background-color: green; width:30%;">최대 분석 오차율: {max_val_avg:.2f}%</h4>', unsafe_allow_html=True)

    st.markdown("##### 4. 보행 파라미터 regression 성능 (R-squared; r2)")
    df_gt = pd.DataFrame(df_all_results['정답값'].values.reshape(-1, len(target_columns)), columns=target_columns)
    df_pred = pd.DataFrame(df_all_results['예측값'].values.reshape(-1, len(target_columns)), columns=target_columns)

    # Use only evaluation columns for R² calculation
    df_gt_eval = df_gt[eval_columns]
    df_pred_eval = df_pred[eval_columns]

    r2_score_table  = pd.Series(r2_score(df_gt_eval, df_pred_eval, multioutput='raw_values'), index=eval_columns, name='r2').to_frame().T
    st.dataframe(r2_score_table)

    return df_all_results_agg

def draw_scatter_plots(df_all_results_agg, enable_calibration=False):
    # 산점도 (정답값 vs 예측값)
    if enable_calibration:
        st.markdown('##### 보행 파라미터 산점도 - Velocity 제외')
        st.info("ℹ️ Velocity는 보정 시스템에서 정답값을 직접 사용하므로 시각화에서 제외됩니다.")
    else:
        st.markdown('##### 보행 파라미터 산점도')

    df_all_results_agg = df_all_results_agg.reset_index()
    for i, _tab in enumerate(st.tabs(eval_columns)):
        with _tab:
            # 데이터 필터링
            scatter_data = df_all_results_agg[df_all_results_agg['index'] == eval_columns[i]]  # 선택된 파라미터만 필터링

            # y_true와 y_pred의 범위 계산
            y_true_min = scatter_data['정답값'].min()
            y_true_max = scatter_data['정답값'].max()
            y_pred_min = scatter_data['예측값'].min()
            y_pred_max = scatter_data['예측값'].max()

            # 최소-최대 범위를 조정하여 padding을 추가 (옵션)
            padding = 0.05 * (y_true_max - y_true_min)  # 범위의 5%를 여유 공간으로 사용
            plot_min = min(y_true_min, y_pred_min) - padding
            plot_max = max(y_true_max, y_pred_max) + padding

            # 산점도와 일치선 (y = x)을 그리기 위한 vega_lite 설정
            st.vega_lite_chart(
                scatter_data,
                {
                    "layer": [
                        {
                            "mark": "circle",
                            "encoding": {
                                "x": {
                                    "field": "정답값",
                                    "type": "quantitative",
                                    "title": "y_true",
                                    "scale": {"domain": [plot_min, plot_max]},  # x축 범위 설정
                                },
                                "y": {
                                    "field": "예측값",
                                    "type": "quantitative",
                                    "title": "y_pred",
                                    "scale": {"domain": [plot_min, plot_max]},  # y축 범위 설정
                                },
                                "color": {
                                    "field": "index",
                                    "type": "nominal",
                                    "title": "보행 파라미터",
                                    "sort": eval_columns,  # 색상 순서를 eval_columns 순서대로 지정
                                },
                                "shape": {
                                    "field": "index",
                                    "type": "nominal",
                                    "title": "보행 파라미터 모양",
                                    "sort": eval_columns,  # 모양 순서도 eval_columns 순서대로 지정
                                },
                                "tooltip": [
                                    {"field": "ID", "type": "nominal"},
                                    {"field": "정답값", "type": "quantitative", "title": "정답값"},
                                    {"field": "예측값", "type": "quantitative", "title": "예측값"},
                                    {"field": "분석 오차율 (%)", "type": "quantitative", "title": "분석 오차율 (%)"},
                                ],  # hover 시 정답값과 예측값 표시
                            },
                        },
                        {
                            "mark": {
                                "type": "line",
                                "color": "red",
                                "size": 2,
                                "strokeDash": [5, 5],  # 점선 스타일로 설정
                            },
                            "encoding": {
                                "x": {
                                    "field": "정답값",
                                    "type": "quantitative",
                                    "scale": {"domain": [plot_min, plot_max]},  # x축 범위 설정
                                },
                                "y": {
                                    "field": "정답값",
                                    "type": "quantitative",
                                    "scale": {"domain": [plot_min, plot_max]},  # y축 범위 설정
                                },
                            },
                        }
                    ]
                },
                use_container_width=True,
            )

def rename_videos(src, dst):
    # 디렉토리 설정
    src_dir = Path(src)
    dst_dir = Path(dst)

    # 대상 디렉터리 생성 (존재하지 않을 경우)
    dst_dir.mkdir(parents=True, exist_ok=True)

    # 모든 .avi 파일 읽기 및 정렬 (파일명 기준 시간순)
    files = sorted(src_dir.glob("*.avi"), key=lambda f: f.stem)

    # 그룹별 파일을 저장할 딕셔너리
    grouped_files = {"Walk1": [], "Walk2": []}

    # 그룹에 따라 파일 분류
    for file in files:
        if "Walk1" in file.stem:
            grouped_files["Walk1"].append(file)
        elif "Walk2" in file.stem:
            grouped_files["Walk2"].append(file)

    # Pair ID 생성 및 파일 이동/리네임
    id_counter = 1
    for walk1_file, walk2_file in zip(grouped_files["Walk1"], grouped_files["Walk2"]):
        # ID 생성
        gait_id = f"{id_counter:02d}"
        id_counter += 1

        # Walk1 파일 이동 및 리네임
        walk1_new_name = f"{walk1_file.stem}_{gait_id}{walk1_file.suffix}"
        walk1_new_path = dst_dir / walk1_new_name
        shutil.copy(str(walk1_file), walk1_new_path)
        print(f"Moved: {walk1_file.name} → {walk1_new_path}")

        # Walk2 파일 이동 및 리네임
        walk2_new_name = f"{walk2_file.stem}_{gait_id}{walk2_file.suffix}"
        walk2_new_path = dst_dir / walk2_new_name
        shutil.copy(str(walk2_file), walk2_new_path)
        print(f"Moved: {walk2_file.name} → {walk2_new_path}")


def write_video_from_bytes(byte_data, output_path, add_noise=False, trim_video=False):
    """
    바이트 데이터를 읽어 비디오 파일로 저장합니다.
    
    Args:
        byte_data (bytes): 동영상 파일의 바이트 데이터.
        output_path (str): 저장할 동영상 경로.
    """
    # 바이트 데이터를 임시 파일로 저장
    with tempfile.NamedTemporaryFile(delete=False, suffix=".avi") as tmp_file:
        tmp_file.write(byte_data)
        tmp_file_path = tmp_file.name

    if add_noise:
        add_noise_to_video(tmp_file_path, kernel_size=(7, 7))
        tmp_file_path = str(Path(tmp_file_path).with_stem("proc"))

    if trim_video:
        print('target_distance:', target_distance)
        # 비디오 자르기
        trim_center_segment(tmp_file_path, output_path, original_distance=4.8, target_distance=target_distance)
        tmp_file_path = str(Path(tmp_file_path).with_stem("proc"))

    # 임시 파일을 VideoFileClip으로 처리
    clip = VideoFileClip(tmp_file_path)

    # 동영상 저장
    clip.write_videofile(output_path, codec="libx264", fps=clip.fps)
    print(f"Video saved at {output_path}")

    new_byte_data = open(tmp_file_path, 'rb').read()
    
    # 임시 파일 삭제
    os.remove(tmp_file_path)

    return new_byte_data


st.title('✅ 게이트 스캐너 성능 평가 (ML 보정 시스템 포함)')

tab1, tab2, tab3, tab4, tab5 = st.tabs(['데이터 불러오기', '샘플 테스트', '전체 결과', '업로드(샘플 테스트)', '업로드(일괄 테스트)'])

df_test = load_dataset()
test_vids = df_test.index.tolist()
user_ids = np.unique([ x.split('_')[0] for x in test_vids ])[:457]
with tab1:
    st.markdown("#### 📚 `GAITRite`에서 수집된 레이블 데이터 (평가용)")
    with st.expander('데이터 분할 시 사용된 코드', icon='💻'):
        st.code("""
                from sklearn.model_selection import train_test_split

                def split_dataset_with_vids(input_df, target_df, vids, test_size=0.2, random_state=42):
                    # 학습 데이터 : 80%, 테스트 데이터 : 20% 비율로 데이터셋 분할
                    train_vids, test_vids = train_test_split(
                        vids, test_size=test_size, random_state=random_state
                    )

                    train_X, train_y = (
                        filter_input_df_with_vids(input_df, train_vids),
                        filter_target_df_with_vids(target_df, train_vids),
                    )

                    test_X, test_y = (
                        filter_input_df_with_vids(input_df, test_vids),
                        filter_target_df_with_vids(target_df, test_vids),
                    )

                    return train_X, train_y, train_vids, test_X, test_y, test_vids
                """)
    st.markdown(f'전체 비디오 개수 : `{len(df_test)}` 개, 대상자 수 : `{len(user_ids)}`명')
    st.dataframe(df_test.reset_index(drop=True), use_container_width=True)

# Ensure session state for video management
if "selected_video" not in st.session_state:
    st.session_state.selected_video = None
if "df_all_results" not in st.session_state:
    st.session_state.df_all_results = None

with tab2:
    col1, col2 = st.columns([0.3, 0.7])

    def remove_output_video():
        if os.path.exists('./output.mp4'):
            os.remove('./output.mp4')

    with col1:
        # Handle video selection
        video_file = st.selectbox(
            "샘플 비디오 선택",
            test_vids,
            on_change=remove_output_video
        )

        video_path = os.path.join(video_root, video_file) + '.avi'

        # Handle video reload logic
        if st.session_state.selected_video != video_file:
            st.session_state.selected_video = video_file
            remove_output_video()
            st.rerun()

        # Convert video using ffmpeg
        if not os.path.exists('./output.mp4'):
            with st.spinner('Processing video...'):
                subprocess.run(
                    f'ffmpeg-7.0.2-amd64-static/ffmpeg -y -i {video_path} -vcodec libx264 -crf 23 output.mp4 -hide_banner -loglevel quiet'.split()
                )

        st.markdown('##### 🎬 선택된 비디오')
        if os.path.exists('./output.mp4'):
            st.video('output.mp4', autoplay=True, loop=False)
        else:
            st.text("비디오가 존재하지 않습니다.")
        invoke_btn = st.button('🤖 AI 분석 요청!', type='primary', use_container_width=True)
    
    if invoke_btn:
        video_bytes = open(video_path, 'rb').read()
        new_video_bytes = write_video_from_bytes(video_bytes, "output.mp4", add_noise=False, trim_video=enable_trim_video)

        # case_id에 해당하는 정답값 가져오기 (calibration을 위해 먼저 가져옴)
        case_id = Path(video_path).stem
        y_true = df_test.loc[case_id]
        y_true.name = '정답값'

        with st.spinner('Analyzing gait video...'):
            res = invoke_api(new_video_bytes)

            # Apply calibration using ground truth velocity
            if enable_calibration and 'Velocity' in y_true:
                measured_velocity = float(y_true['Velocity'])
                calibrated_res = integrate_calibration_with_main(
                    api_response_json=res,
                    measured_velocity=measured_velocity,
                    enable_calibration=True
                )
                # Show calibration status
                calibration_data = json.loads(calibrated_res)
                if 'calibration_info' in calibration_data:
                    calib_info = calibration_data['calibration_info']
                    st.success(f"🔧 Calibration applied! Corrected {calib_info['corrections_applied']}/{calib_info['total_parameters_processed']} parameters using velocity {measured_velocity:.1f} cm/s")
                res = calibrated_res
            else:
                if not enable_calibration:
                    st.info("ℹ️ Calibration disabled - using original API predictions")
                else:
                    st.warning("⚠️ Ground truth velocity not available - using original API predictions")

        y_pred = pd.read_json(res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns]
        y_pred.name = '예측값'

        with col2:
            df_res = pd.concat([y_true, y_pred], axis=1)
            df_res['분석 오차율 (%)'] = df_res.apply(compute_mape, axis=1)

            tab21, tab22 = st.tabs(['📝 성능표', '📊 시각화'])

            with tab21:
                if enable_calibration:
                    st.markdown('##### 1. 보행 파라미터별 성능 (보행 분석 오차율) - Velocity 제외')
                    st.info("ℹ️ Velocity는 보정 시스템에서 정답값을 직접 사용하므로 평가에서 제외됩니다.")
                else:
                    st.markdown('##### 1. 보행 파라미터별 성능 (보행 분석 오차율)')

                st.dataframe(df_res.T, use_container_width=True)

                # Only show statistics for evaluation columns
                df_res_eval = df_res.loc[eval_columns]
                df_res_stats = df_res_eval[['분석 오차율 (%)']].describe().iloc[:2, :].T
                st.markdown('##### 2. 성능 분포')
                st.dataframe(df_res_stats)
                with st.expander("평가지표 산출식", icon='📌'):
                    st.latex(r"""
                            \text{MAPE}_i = \frac{1}{n} \sum_{i=1}^{n} \left| \frac{y_{\text{true},i} - y_{\text{pred},i}}{y_{\text{true},i}} \right| \times 100 [\%]
                        """)
            
            with tab22:
                if enable_calibration:
                    st.info("ℹ️ Velocity는 보정 시스템에서 정답값을 직접 사용하므로 시각화에서 제외됩니다.")

                # 예측값과 실제값을 나란히 표시하여 비교 (evaluation columns만 사용)
                df_res_eval = df_res.loc[eval_columns].copy().reset_index()  # 인덱스 초기화
                df_res_eval.rename({'index': '보행 파라미터'}, axis=1, inplace=True)

                # 예측값과 실제값을 나란히 비교하기 위한 형태로 변환
                df_res_long = pd.melt(df_res_eval, id_vars=['보행 파라미터'], value_vars=['정답값', '예측값'])
                df_res_long.rename(columns={'variable': '값 종류', 'value': '값'}, inplace=True)

                df_res_long['보행 파라미터'] = pd.Categorical(df_res_long['보행 파라미터'], categories=eval_columns, ordered=True)

                # 바 차트 시각화
                st.bar_chart(df_res_long, x="보행 파라미터", y="값", color="값 종류", stack=False)



with tab3:
    # 전체 비디오 분석 (최초 실행 시 한 번만)
    import time  # 시간을 측정하기 위한 모듈

    def analyze_all_videos(enable_calibration_param):
        """
        Analyze all videos with calibration-aware performance evaluation.

        Args:
            enable_calibration_param (bool): Whether calibration is enabled
        """
        all_results = []
        # 진행 상태를 추적할 변수들
        estimated_time_text = st.empty()  # 예상 시간 표시용 빈 공간
        num_videos = len(test_vids)  # 비디오 총 개수

        start_time = time.time()  # 시작 시간

        cnt = 0
        calibration_count = 0
        for idx, video_file in enumerate(stqdm(test_vids, desc="Processing videos")):
            video_path = os.path.join(video_root, video_file) + '.avi'

            video_bytes = open(video_path, 'rb').read()
            new_video_bytes = write_video_from_bytes(video_bytes, "output.mp4", add_noise=False, trim_video=enable_trim_video)

            id_ = video_file.split('_')[0]

            try:
                # Get ground truth data first for calibration
                case_id = Path(video_path).stem
                y_true = df_test.loc[case_id]
                y_true.name = '정답값'

                # 분석 수행
                res = invoke_api(new_video_bytes)

                # Apply calibration using ground truth velocity
                if enable_calibration_param and 'Velocity' in y_true:
                    measured_velocity = float(y_true['Velocity'])
                    res = integrate_calibration_with_main(
                        api_response_json=res,
                        measured_velocity=measured_velocity,
                        enable_calibration=True
                    )
                    calibration_count += 1

                y_pred = pd.read_json(res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns]
                y_pred.name = '예측값'
                df_res = pd.concat([y_true, y_pred], axis=1)
                df_res['ID'] = id_
                all_results.append(df_res.reset_index())
                cnt += 1
            except Exception as e:
                # 오류 처리
                st.error(f"비디오 {video_file} 처리 중 오류 발생: {e}")

            estimated_time_text.write(string.ascii_letters)

        # 처리 시간 측정
        elapsed_time = time.time() - start_time  # 경과 시간

        # 처리에 소요된 시간 표시
        elapsed_minutes, elapsed_seconds = divmod(elapsed_time, 60)
        calibration_info = f", 🔧 보정 적용: {calibration_count}/{cnt}" if enable_calibration_param else ""

        # Add evaluation column info when calibration is enabled
        eval_info = f", 📊 평가 대상: {len(eval_columns)}개 파라미터" if enable_calibration_param else f", 📊 평가 대상: {len(target_columns)}개 파라미터"

        estimated_time_text.write(f"처리에 소요된 시간: {int(elapsed_minutes)}분 {int(elapsed_seconds)}초, 비디오 분석 성공률 : {cnt / num_videos * 100}% (🟢: {cnt}, ❌: {num_videos-cnt}){calibration_info}{eval_info}")

        # Show evaluation column details when calibration is enabled
        if enable_calibration_param:
            excluded_info = st.empty()
            excluded_info.info(f"ℹ️ 보정 모드에서는 Velocity가 평가에서 제외됩니다. 평가 대상: {', '.join(eval_columns)}")

        # 모든 결과를 합침
        df_all_results = pd.concat(all_results, ignore_index=True)

        # Calculate quick performance summary using evaluation columns only
        if len(all_results) > 0:
            # Calculate MAPE for evaluation columns only
            eval_results = []
            for result in all_results:
                # Filter to evaluation columns only for performance calculation
                eval_data = result.set_index('index').loc[eval_columns]
                eval_data['분석 오차율 (%)'] = eval_data.apply(compute_mape, axis=1)
                eval_results.append(eval_data['분석 오차율 (%)'].mean())

            avg_mape = np.mean(eval_results)

            # Show quick performance summary
            perf_summary = st.empty()
            if enable_calibration_param:
                perf_summary.success(f"🎯 빠른 성능 요약 (Velocity 제외): 평균 MAPE = {avg_mape:.2f}% ({len(eval_columns)}개 파라미터 기준)")
            else:
                perf_summary.success(f"🎯 빠른 성능 요약: 평균 MAPE = {avg_mape:.2f}% ({len(eval_columns)}개 파라미터 기준)")

        return df_all_results

    
    st.markdown("#### ⭐️ 전체 시험 데이터 분석")
    invoke_btn_all = st.button('🔥 전체 데이터 분석 시작!')

    if invoke_btn_all:
        # 분석 데이터 로드 (calibration 상태 전달)
        st.session_state.df_all_results = analyze_all_videos(enable_calibration)

    if st.session_state.df_all_results is not None:
        df_all_results = st.session_state.df_all_results

        # Show calibration status
        if enable_calibration:
            st.success("🔧 ML 보정이 적용된 결과입니다. 정확한 속도 측정값을 활용하여 다른 보행 파라미터를 보정했습니다.")
        else:
            st.info("ℹ️ 원본 API 예측 결과입니다. 보정이 비활성화되어 있습니다.")

        with st.expander("평가지표 산출식", icon='📌'):
                st.latex(r"""
                        \text{MAPE}_i = \frac{1}{n} \sum_{i=1}^{n} \left| \frac{y_{\text{true},i} - y_{\text{pred},i}}{y_{\text{true},i}} \right| \times 100 [\%]
                    """)
                st.latex(r"""
                    \text{Micro Average} = \frac{1}{N} \sum_{i=1}^{N} \text{MAPE}_i [\%]
                """)

        tab31, tab32 = st.tabs(['📝 성능표', '📊 시각화'])

        with tab31:
            df_all_results_agg = report_performance(df_all_results, enable_calibration)
        with tab32:
            draw_scatter_plots(df_all_results_agg, enable_calibration)

with tab4:
    video_file = st.file_uploader('비디오 업로드', type=['mp4', 'avi'])
    if video_file:
        col41, col42 = st.columns([0.3, 0.7])
        with col41:
            # 비디오를 새 파일로 저장
            video_bytes = video_file.read()
            with st.spinner("Processing video"):
                # TODO: 서버 API 코드에 노이즈 추가
                new_video_bytes = write_video_from_bytes(video_bytes, "output_upload.mp4", add_noise=False, trim_video=True)
            st.markdown("비디오 입력")
            st.video('output_upload.mp4')

        with col42:
            st.markdown('##### 분석 결과')

            # Note about calibration for uploaded videos
            if enable_calibration:
                st.info("ℹ️ Calibration not available for uploaded videos (no ground truth velocity)")

            # 분석 수행
            res = invoke_api(new_video_bytes)
            # No calibration applied for uploaded videos since no ground truth is available
            y_pred = pd.read_json(res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns].T
            y_pred.name = '예측값'
            st.dataframe(y_pred)

with tab5:
    gt_data = None
    excel_file = st.file_uploader("Upload a Excel file", type=['xlsx'])
    if excel_file:
        try:
            gt_data = pd.read_excel(excel_file.read()).rename(rename_map, axis=1)
        except Exception as e:
            # 오류 처리
            st.info(f"올바른 엑셀 파일 형식이 아닙니다. {target_columns}를 포함하고 있어야 합니다!")
            gt_data = None
        else:
            gt_data = gt_data[target_columns]
            st.dataframe(gt_data.head())
    
    current_dir = Path.cwd()


    # 폴더만 리스트업
    folders = [ str(item) for item in current_dir.iterdir() if item.is_dir() ]

    video_file_root = st.selectbox('비디오 폴더 경로', ['선택해주세요'] + folders)
    if video_file_root == '선택해주세요':
        video_file_list = []
    else:
        src_video_dir = video_file_root
        dst_video_dir = video_file_root + '_processed'
        if os.path.exists(dst_video_dir):
            shutil.rmtree(dst_video_dir)
            os.makedirs(dst_video_dir)
        rename_videos(src=video_file_root, dst=dst_video_dir)
        video_file_list = natsorted(Path(dst_video_dir).glob('*'))

    trigger_btn = st.button('🔥 업로드된 데이터 분석 시작!')

    if trigger_btn:
        if (gt_data is not None) and video_file_list:
            all_results_new = []
            calibration_count = 0

            for idx, video_file in enumerate(stqdm(video_file_list, desc="Processing videos")):
                id_ = video_file.stem.split('_')[-1] # last one is ID string

                # Get ground truth data first for calibration
                y_true = gt_data.iloc[idx]
                y_true.name = '정답값'

                # 분석 수행
                res = invoke_api(str(video_file))

                # Apply calibration using ground truth velocity if available
                if enable_calibration and 'Velocity' in y_true:
                    measured_velocity = float(y_true['Velocity'])
                    res = integrate_calibration_with_main(
                        api_response_json=res,
                        measured_velocity=measured_velocity,
                        enable_calibration=True
                    )
                    calibration_count += 1

                y_pred = pd.read_json(res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns]
                y_pred.name = '예측값'
                df_res = pd.concat([y_true, y_pred], axis=1)
                df_res['ID'] = id_
                all_results_new.append(df_res.reset_index())

            df_all_results_new = pd.concat(all_results_new, ignore_index=True)

            # Show calibration summary
            if enable_calibration:
                st.success(f"🔧 Calibration applied to {calibration_count}/{len(video_file_list)} videos")
            else:
                st.info("ℹ️ Calibration disabled - using original API predictions")

            tab51, tab52 = st.tabs(['📝 성능표', '📊 시각화'])

            with tab51:
                df_all_results_agg_new = report_performance(df_all_results_new, enable_calibration)
            with tab52:
                draw_scatter_plots(df_all_results_agg_new, enable_calibration)
        else:
            st.warning("엑셀 파일과 비디오가 모두 준비되어야 분석을 진행할 수 있습니다.")

"""
Example integration of calibration system with main.py

This file shows how to modify the existing main.py to include calibration functionality.
The key changes are:
1. Import the calibration module
2. Add UI controls for measured velocity
3. Apply calibration to API responses when measured velocity is available
"""

import json
import pandas as pd
import streamlit as st
from pathlib import Path

# Import the calibration system
from api_calibration import integrate_calibration_with_main

# Example of how to modify the existing invoke_api function
def invoke_api_with_calibration(video_file, measured_velocity=None, enable_calibration=True):
    """
    Modified version of invoke_api that includes calibration support.
    
    Args:
        video_file: Video file to analyze
        measured_velocity: Optional measured velocity from distance sensor (cm/s)
        enable_calibration: Whether to apply calibration
    
    Returns:
        str: API response JSON (calibrated if velocity provided)
    """
    # Original API call (this would be your existing invoke_api function)
    if isinstance(video_file, str):
        video_file = open(video_file, 'rb')

    payload = {}
    files=[
        ('video_file',('filename', video_file, 'application/octet-stream')),
    ]
    headers = {
        'Accept': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJob3NzYXkiLCJleHAiOjE3ODkwMjA1MTEsInR5cGUiOiJhY2Nlc3MifQ.Ant7sKKrywI2EKv0tp4kc3mYqME0B_qKuKKBpERBYNs'
    }

    # This would be your actual API call
    # response = requests.request("POST", url, headers=headers, data=payload, files=files)
    # api_response_json = json.dumps(response.json())
    
    # For demonstration, using example response
    example_response = {
        "gait_parameters": {
            "Velocity": 100.3327,
            "Cadence": 101.2393,
            "Cycle_Time_L": 1.1798,
            "Cycle_Time_R": 1.1814,
            "Stride_Len_L": 118.9,
            "Stride_Len_R": 118.9281,
            "Supp_Base_L": 9.3145,
            "Supp_Base_R": 9.2847,
            "Swing_Perc_L": 38.8685,
            "Swing_Perc_R": 38.8074,
            "Stance_Perc_L": 61.134,
            "Stance_Perc_R": 61.1955,
            "D_Supp_Perc_L": 22.5706,
            "D_Supp_Perc_R": 22.5472,
            "ToeInOut_L": 2.6259,
            "ToeInOut_R": 7.5333,
            "StrideLen_CV_L": 8.9616,
            "StrideLen_CV_R": 7.7936,
            "StrideTm_CV_L": 49.9642,
            "StrideTm_CV_R": 46.44
        },
        "gait_score": 9.298256544875823,
        "status_code": 200,
        "message": "success"
    }
    api_response_json = json.dumps(example_response)
    
    # Apply calibration if measured velocity is available
    calibrated_response_json = integrate_calibration_with_main(
        api_response_json=api_response_json,
        measured_velocity=measured_velocity,
        enable_calibration=enable_calibration
    )
    
    return calibrated_response_json


def demo_streamlit_integration():
    """
    Demonstrate how to integrate calibration into Streamlit UI
    """
    st.title('🔧 Gait Analysis with Calibration')
    
    # Add calibration controls to sidebar
    with st.sidebar:
        st.header("Calibration Settings")
        enable_calibration = st.checkbox("Enable Calibration", value=True)
        measured_velocity = st.number_input(
            "Measured Velocity (cm/s)", 
            min_value=0.0, 
            max_value=200.0, 
            value=95.0,
            step=1.0,
            help="Velocity measured by distance sensor"
        )
        
        if not enable_calibration:
            st.info("Calibration is disabled. Original API response will be used.")
        elif measured_velocity <= 0:
            st.warning("Please enter a valid measured velocity for calibration.")
    
    # Main content
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("Analysis")
        
        # Simulate video analysis button
        if st.button("🤖 Analyze Gait", type="primary"):
            with st.spinner("Analyzing gait video..."):
                # Use the modified API function
                res = invoke_api_with_calibration(
                    video_file="dummy_video.avi",  # This would be your actual video
                    measured_velocity=measured_velocity if enable_calibration and measured_velocity > 0 else None,
                    enable_calibration=enable_calibration
                )
            
            # Process the response (same as existing code)
            response_data = json.loads(res)
            
            # Check if calibration was applied
            calibration_applied = 'calibration_info' in response_data
            
            if calibration_applied:
                st.success("✅ Calibration applied successfully!")
                calib_info = response_data['calibration_info']
                st.info(f"Corrections applied to {calib_info['corrections_applied']}/{calib_info['total_parameters_processed']} parameters")
            else:
                st.info("ℹ️ No calibration applied (original API response)")
            
            # Display results
            gait_params = response_data['gait_parameters']
            
            with col2:
                st.header("Results")
                
                # Create DataFrame for display
                params_df = pd.DataFrame([
                    {"Parameter": k, "Value": f"{v:.3f}"} 
                    for k, v in gait_params.items()
                    if k in ['Velocity', 'Stride_Len_L', 'Stride_Len_R', 'Swing_Perc_L', 'Swing_Perc_R']
                ])
                
                st.dataframe(params_df, use_container_width=True)
                
                # Show calibration details if applied
                if calibration_applied:
                    with st.expander("Calibration Details"):
                        calib_info = response_data['calibration_info']
                        st.json(calib_info)


def show_main_py_modifications():
    """
    Show the specific modifications needed for main.py
    """
    st.header("Required Modifications for main.py")
    
    st.subheader("1. Add Import")
    st.code("""
# Add this import at the top of main.py
from api_calibration import integrate_calibration_with_main
""", language="python")
    
    st.subheader("2. Add UI Controls")
    st.code("""
# Add to sidebar (around line 76-78)
with st.sidebar:
    target_distance = st.slider('target_distance', 1.0, 5.0, 3.0, step=0.1)
    
    # Add calibration controls
    st.header("Calibration Settings")
    enable_calibration = st.checkbox("Enable Calibration", value=True)
    measured_velocity = st.number_input(
        "Measured Velocity (cm/s)", 
        min_value=0.0, 
        max_value=200.0, 
        value=None,
        help="Leave empty if no distance sensor measurement available"
    )
""", language="python")
    
    st.subheader("3. Modify API Response Processing")
    st.code("""
# In tab2 (around line 487-494), modify the API call section:
if invoke_btn:
    video_bytes = open(video_path, 'rb').read()
    new_video_bytes = write_video_from_bytes(video_bytes, "output.mp4", add_noise=False, trim_video=enable_trim_video)
    with st.spinner('Analyzing gait video...'):
        # Original API call
        res = invoke_api(new_video_bytes)
        
        # Apply calibration if measured velocity is available
        calibrated_res = integrate_calibration_with_main(
            api_response_json=res,
            measured_velocity=measured_velocity if enable_calibration and measured_velocity else None,
            enable_calibration=enable_calibration
        )

    # Continue with existing processing using calibrated_res instead of res
    y_pred = pd.read_json(calibrated_res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns]
    # ... rest of existing code remains the same
""", language="python")
    
    st.subheader("4. Similar Changes for Other Tabs")
    st.code("""
# Apply similar changes to:
# - Tab3 (full analysis): around line 558
# - Tab4 (upload sample): around line 631  
# - Tab5 (batch upload): around line 677

# Example for tab3:
res = invoke_api(new_video_bytes)
calibrated_res = integrate_calibration_with_main(
    api_response_json=res,
    measured_velocity=measured_velocity if enable_calibration and measured_velocity else None,
    enable_calibration=enable_calibration
)
y_pred = pd.read_json(calibrated_res).T.rename(rename_map, axis=1).loc['gait_parameters', target_columns]
""", language="python")


if __name__ == "__main__":
    # Run the Streamlit demo
    demo_streamlit_integration()
    
    # Show modification instructions
    show_main_py_modifications()

# Gait Parameter Calibration using AutoML (Multi-Output)

이 프로젝트는 정확한 Velocity 측정값을 활용하여 딥러닝 모델의 다른 gait parameter 예측을 보정하는 AutoML 기반 **멀티 아웃풋** 시스템입니다.

## 배경

딥러닝 모델로 gait parameter를 추론할 때 속도 측정이 부정확하여 다른 변수들에 악영향을 주는 문제가 있습니다. 이 프로젝트는 다음 가정에 기반합니다:

- **가정**: Velocity와 다른 gait parameters 간에는 상관관계가 존재한다
- **목표**: 정확한 Velocity가 주어졌을 때 다른 gait parameters를 보정할 수 있다
- **핵심 개선**: **1:N 예측** - 하나의 Velocity 값으로 모든 gait parameters를 동시에 예측

## 주요 기능

1. **🧹 데이터 전처리**: 아웃라이어 자동 탐지 및 제거 (IQR, Z-score, Isolation Forest)
2. **📊 데이터 탐색**: Velocity와 각 gait parameter 간의 상관관계 분석
3. **🤖 앙상블 멀티 아웃풋 모델**: 다중 모델 앙상블로 1:8 동시 예측 (최대 성능 달성)
4. **📈 성능 평가**: R², MSE, MAE 등 다양한 지표로 모델 성능 평가
5. **⚡ 동시 예측**: 단일 Velocity 값으로 8개 핵심 gait parameters 동시 예측
6. **🔍 민감도 분석**: Velocity 변화에 따른 각 parameter의 반응 분석
7. **✅ Ground-Truth 검증**: 실제값과 예측값 간의 correlation 시각화
8. **🔧 속도 기반 후처리**: 거리 센서 속도와 모델 인식 범위 기반 적응적 보정 시스템

## 설치

```bash
# 의존성 설치
pip install -r requirements.txt
```

## 사용법

### 🚀 권장 워크플로우 (전처리 기반)

#### 1단계: 데이터 전처리 및 저장
```bash
python run_calibration.py preprocess
```

#### 2단계: 전처리된 데이터로 모델 학습
```bash
python run_calibration.py train_preprocessed
```

#### 3단계: 앙상블 모델 학습 (최고 성능)
```bash
python run_calibration.py ensemble
```

#### 4단계: 모델 검증
```bash
python run_calibration.py validate
```

### 📊 기존 워크플로우 (호환성 유지)

#### 1. 빠른 데모 실행
```bash
python run_calibration.py demo
```

#### 2. 전체 데이터로 완전한 학습
```bash
python run_calibration.py full
```

#### 3. 저장된 모델로 예측
```bash
python run_calibration.py predict
```

#### 4. Velocity 민감도 분석
```bash
python run_calibration.py sensitivity
```

#### 5. 예측값 Scatter Plot 생성
```bash
python run_calibration.py scatter
```

#### 6. 상관관계 분석
```bash
python run_calibration.py correlation
```

#### 7. Ground-Truth vs Predictions 검증
```bash
python run_calibration.py validate
```

#### 8. 아웃라이어 분석
```bash
python run_calibration.py outliers
```

## 파일 구조

### 핵심 코드
- `gait_automl_calibration.py`: 메인 캘리브레이션 클래스 + 전처리 기능
- `run_calibration.py`: 통합 실행 스크립트 (10가지 모드)
- `calibration_utils.py`: 실시간 예측 및 유틸리티
- `integration_example.py`: 딥러닝 모델 통합 예시
- `requirements.txt`: 필요한 패키지 목록

### 데이터 디렉토리
- `data/`: 전처리된 데이터 저장소
  - `preprocessed_gait_data_iqr.csv`: IQR 방법으로 전처리된 데이터
  - `preprocessed_gait_data_iqr_metadata.json`: 전처리 메타데이터

### 모델 디렉토리
- `models/`: 학습된 모델 저장소
  - `multi_output_model.pkl`: 멀티 아웃풋 AutoML 모델
  - `velocity_scaler.pkl`: Velocity 스케일러
  - `target_columns.pkl`: Target parameter 정보

## 예측 대상 Parameters (8개)

- `Stride_Len_L`, `Stride_Len_R`: 좌우 보폭 길이 (최고 성능, R² 0.89+)
- `Swing_Perc_L`, `Swing_Perc_R`: 좌우 스윙 비율 (양호 성능, R² 0.6+)
- `Stance_Perc_L`, `Stance_Perc_R`: 좌우 지지 비율 (양호 성능, R² 0.6+)
- `D_Supp_PercL`, `D_Supp_PercR`: 좌우 이중지지 비율 (우수 성능, R² 0.75+)

**참고**: Stride Time parameters(`StrideTm_L`, `StrideTm_R`)는 낮은 상관관계(R² 0.18)로 인해 **완전히 제외**되었습니다.

## 사용 예시

### 🚀 권장 방법 (전처리 기반)

```python
from gait_automl_calibration import preprocess_and_save_data, train_from_saved_data
from calibration_utils import GaitCalibrationPredictor

# === 1단계: 데이터 전처리 및 저장 ===
saved_path = preprocess_and_save_data(outlier_method='iqr', visualize=True)
# 결과: ./data/preprocessed_gait_data_iqr.csv 저장
# 17,900 → 16,233 샘플 (1,667개 아웃라이어 제거)

# === 2단계: 전처리된 데이터로 모델 학습 ===
calibrator = train_from_saved_data(
    data_path='./data/preprocessed_gait_data_iqr.csv',
    automl_time_budget=600
)
# 결과: Stride Length R² 0.89+ 달성!

# === 3단계: 실시간 예측 ===
predictor = GaitCalibrationPredictor()

# 단일 velocity로 모든 parameters 동시 예측
velocity = 85  # cm/s (거리 센서로 정확히 측정)
all_predictions = predictor.predict_single(velocity)
print(f"Velocity {velocity} cm/s -> All gait parameters:")
for param, value in all_predictions.items():
    print(f"  {param}: {value:.3f}")

# 배치 예측
velocities = [60, 80, 100, 120]  # cm/s
batch_predictions = predictor.predict_batch(velocities)
print(batch_predictions)
```

### 📊 기존 방법 (호환성 유지)

```python
from gait_automl_calibration import GaitParameterCalibrator
from calibration_utils import GaitCalibrationPredictor

# 전통적인 방법 (실시간 전처리 포함)
calibrator = GaitParameterCalibrator(automl_time_budget=300)
df = calibrator.load_data()
df = calibrator.remove_outliers(method='iqr')  # 실시간 전처리
calibrator.prepare_data()
calibrator.train_automl_models()
calibrator.save_models()
```

### 🤖 앙상블 모델 학습 (최고 성능)

```python
from gait_automl_calibration import PreprocessedGaitCalibrator

# 앙상블 모델 학습
calibrator = PreprocessedGaitCalibrator(
    preprocessed_data_path='./data/preprocessed_gait_data_iqr.csv',
    automl_time_budget=1800  # 30분
)

# 데이터 로드 및 준비
calibrator.load_data()
calibrator.prepare_data()

# 앙상블 모델 학습 (3가지 방법 지원)
calibrator.train_ensemble_models(
    n_models=5,                    # 앙상블할 모델 개수 (3-8)
    ensemble_method='stacking'     # 'voting', 'stacking', 'blending'
)

# 앙상블 모델 평가
results_df = calibrator.evaluate_ensemble_models()

# 모델 저장
calibrator.save_models()

# 결과: 평균 R² 0.7083 (70.83%) - 단일 모델 대비 향상!
```

### 🔧 속도 기반 후처리 시스템

```python
from calibration_utils import GaitCalibrationPredictor

# 후처리 시스템 초기화
predictor = GaitCalibrationPredictor()

# 부정확한 모델 출력 (모델 인식 범위 밖)
noisy_predictions = {
    'Velocity': 180.0,      # 모델 범위(30-150) 밖
    'Stride_Len_L': 165.0,  # 과대 예측
    'Stride_Len_R': 158.2,  # 과대 예측
    'Swing_Perc_L': 42.8,   # 부정확
    'Swing_Perc_R': 41.1,   # 부정확
    'Stance_Perc_L': 57.2,  # 불일치 (Swing과 합이 100%가 아님)
    'Stance_Perc_R': 58.9,  # 불일치
    'D_Supp_PercL': 16.5,
    'D_Supp_PercR': 17.8
}

# 거리 센서로 측정된 실제 속도
measured_velocity = 195.0  # 모델 예측보다도 더 빠름
model_velocity_range = (30.0, 150.0)  # 모델 학습 범위

# 속도 기반 후처리 적용
corrected_predictions = predictor.postprocess_model_output(
    model_predictions=noisy_predictions,
    measured_velocity=measured_velocity,        # 거리 센서 측정값
    model_velocity_range=model_velocity_range,  # 모델 인식 범위
    use_velocity_constraint=True,               # Velocity 일관성 검증
    velocity_tolerance=8.0                      # 허용 오차 범위
)

# 결과: 범위 초과 정도에 따라 자동 조절된 보정 강도!
# 범위 초과 +45 cm/s → 보정 가중치 0.66 → 강한 보정 적용
print("보정된 예측값:")
for param, value in corrected_predictions.items():
    if not param.startswith('_'):  # 메타데이터 제외
        print(f"  {param}: {value:.1f}")
```

## 출력 파일

### 데이터 분석 결과
- `velocity_correlation.png`: Velocity와 각 parameter 간 상관관계
- `velocity_scatterplots.png`: 산점도 매트릭스
- `prediction_results.png`: 멀티 아웃풋 예측 vs 실제값 비교
- `velocity_sensitivity_analysis.png`: 민감도 분석 결과

### 예측값 시각화
- `prediction_scatter_plots_all.png`: 모든 parameters의 예측값 scatter plot
- `prediction_scatter_plots_key.png`: 주요 parameters의 상세 scatter plot (트렌드 라인 포함)
- `prediction_scatter_plots_3d.png`: 3D scatter plot (Velocity vs 두 parameters)
- `predicted_parameters_correlation.png`: 예측된 parameters 간 상관관계 히트맵

### Ground-Truth 검증 결과
- `groundtruth_vs_predictions_all.png`: 모든 parameters의 실제값 vs 예측값 scatter plot
- `groundtruth_vs_predictions_detailed.png`: 주요 parameters의 상세 검증 (회귀선 포함)
- `correlation_r2_summary.png`: Correlation 및 R² 성능 요약 바 차트
- `bland_altman_analysis.png`: **Bland-Altman plot** (평균 residual + zero line + 95% 신뢰구간)

### 아웃라이어 제거 결과
- `outlier_removal_comparison.png`: 아웃라이어 제거 전후 박스플롯 비교
- `outlier_removal_histograms.png`: 아웃라이어 제거 전후 분포 히스토그램

## 저장된 모델 파일

- `models/multi_output_model.pkl`: 멀티 아웃풋 AutoML 모델
- `models/velocity_scaler.pkl`: Velocity 입력 스케일러
- `models/target_columns.pkl`: Target parameter 정보

## 아웃라이어 제거 방법

시스템은 4가지 아웃라이어 탐지 방법을 지원합니다:

### 1. **IQR (Interquartile Range) 방법**
- Q1 - 1.5×IQR ~ Q3 + 1.5×IQR 범위 밖의 값을 아웃라이어로 탐지
- **제거율**: ~9.3% (균형잡힌 접근)
- **권장**: 일반적인 용도

### 2. **Z-Score 방법**
- |Z-score| > 3인 값을 아웃라이어로 탐지
- **제거율**: ~3.1% (보수적 접근)
- **권장**: 데이터 손실을 최소화하고 싶을 때

### 3. **Isolation Forest 방법**
- 머신러닝 기반 이상치 탐지
- **제거율**: ~10.0% (적당히 적극적)
- **권장**: 복잡한 패턴의 아웃라이어 탐지

### 4. **Combined 방법**
- 위 3가지 방법을 모두 적용
- **제거율**: ~13.2% (가장 적극적)
- **권장**: 최고 품질의 데이터가 필요할 때

## 모델 성능

멀티 아웃풋 모델은 다음 지표로 평가됩니다:
- **R² Score**: 결정계수 (높을수록 좋음)
- **MSE**: 평균제곱오차 (낮을수록 좋음)
- **MAE**: 평균절대오차 (낮을수록 좋음)
- **Overall Performance**: 모든 parameters의 평균 성능

### 아웃라이어 제거 효과
- **데이터 품질 향상**: 표준편차 감소, 더 안정적인 분포
- **모델 성능 개선**: 평균 R² Score 향상
- **예측 안정성**: 극값에 의한 편향 감소

## 속도 기반 후처리 시스템

### 🔧 핵심 기능

#### **1. 속도 범위 기반 적응적 보정**
- **모델 범위 내**: 최소 보정 (가중치 0.1-0.3)
- **모델 범위 밖**: 강한 보정 (가중치 0.3-0.9)
- **공식**: `보정값 = ((1-가중치) × 원본값) + (가중치 × ML참조값)`
- **거리 센서 우선**: 측정된 속도를 예측 속도보다 우선 사용

#### **2. 물리적 제약 조건 적용**
- **Stride Length**: 10-200cm 범위 제한
- **Swing/Stance %**: 20-50% / 50-80% 범위 제한
- **Double Support %**: 5-60% 범위 제한
- **비현실적 값 자동 수정**: 물리적으로 불가능한 값 클리핑

#### **3. Velocity 일관성 검증**
- **Stride Length ↔ Velocity 관계**: 일관성 검증 및 자동 조정
- **Swing + Stance = 100%**: 좌우 각각 100% 합계 보장
- **허용 오차 범위**: 사용자 정의 가능 (기본 8-10cm/s)

#### **4. 누락 Parameter 자동 보완**
- **ML 모델 기반**: 누락된 parameter를 ML 예측으로 자동 채움
- **완전한 출력**: 항상 8개 모든 parameter 제공
- **메타데이터 제공**: 보정 정보 및 속도 분석 포함

#### **5. 속도 기반 보정 가중치 계산**
- **범위 내 중앙**: 가중치 0.1 (최소 보정)
- **범위 내 경계**: 가중치 0.3 (약간 보정)
- **범위 밖 근처**: 가중치 0.3-0.6 (중간 보정)
- **범위 밖 멀리**: 가중치 0.6-0.9 (강한 보정)

### 🎯 후처리 시스템 성능

- **평균 보정 정확도**: 79.43% (ML 모델 기반)
- **적응적 보정**: 속도 범위 벗어남 정도에 따라 자동 조절
- **물리적 일관성**: 100% 보장
- **처리 속도**: 실시간 (< 10ms per sample)
- **신뢰도 불필요**: 거리 센서 기반 자동 가중치 계산

## 앙상블 모델 시스템

### 🤖 3가지 앙상블 방법

#### **1. Voting 앙상블 (평균 기반)**
- **방식**: 여러 모델의 예측값을 평균
- **장점**: 단순하고 안정적
- **성능**: 개별 모델 대비 소폭 향상

#### **2. Stacking 앙상블 (메타 모델 기반)**
- **방식**: 기본 모델들의 예측을 메타 모델이 학습
- **장점**: 최고 성능, 모델 간 상호작용 학습
- **성능**: **R² 0.7083** (70.83%) - 최고 성능 달성

#### **3. Blending 앙상블 (홀드아웃 기반)**
- **방식**: 홀드아웃 데이터로 메타 모델 학습
- **장점**: 과적합 방지, 안정적 성능
- **성능**: Stacking과 유사한 성능

### 🎯 앙상블 성능 비교

| 모델 | 평균 Test R² | 성능 향상 |
|------|-------------|----------|
| **Ensemble_stacking** | **0.7083** | **기준** |
| XGBoost_100 | 0.7079 | -0.0004 |
| XGBoost_200 | 0.7062 | -0.0021 |
| GradientBoosting_100 | 0.7018 | -0.0065 |

### 🚀 앙상블 구성 모델

1. **XGBoost_100**: R² 0.7079 (최고 개별 성능)
2. **XGBoost_200**: R² 0.7062 (안정적 성능)
3. **GradientBoosting_100**: R² 0.7018 (다양성 제공)
4. **MLP**: R² 0.7011 (비선형 패턴 학습)
5. **GradientBoosting_200**: R² 0.6931 (추가 다양성)

## Bland-Altman Plot 분석

### 📊 Bland-Altman Plot 특징

#### **1. 다중 기준선 표시**
- **Zero Line (검은색 실선)**: 완벽한 일치 기준선 (차이 = 0)
- **Mean Bias Line (빨간색 점선)**: 평균 residual (시스템적 편향)
- **95% 신뢰구간 (주황색 점선)**: ±1.96 × 표준편차 범위

#### **2. 평균 Residual 값 표시**
- **텍스트 박스**: 각 parameter별 정확한 평균 residual 값
- **시스템적 편향 감지**: 0에서 벗어난 정도로 모델 편향 평가
- **일관성 평가**: 전체 범위에서 편향의 일관성 확인

#### **3. 해석 가이드**
- **Zero Line 근처**: 편향 없는 우수한 예측
- **Mean Bias > 0**: 과소 예측 경향 (실제값이 예측값보다 큼)
- **Mean Bias < 0**: 과대 예측 경향 (예측값이 실제값보다 큼)
- **95% 범위 내**: 대부분의 예측이 허용 가능한 오차 범위

### 🎯 실제 Bland-Altman 결과 예시

```
Stride_Len_L: Mean bias: -0.245 (약간 과대 예측)
Stride_Len_R: Mean bias: +0.123 (약간 과소 예측)
D_Supp_PercL: Mean bias: -0.089 (거의 편향 없음)
```

### 💡 Bland-Altman vs 기존 Residual Plot

| 특징 | 기존 Residual Plot | **Bland-Altman Plot** |
|------|-------------------|----------------------|
| X축 | 예측값 | **평균값 (실제+예측)/2** |
| 편향 표시 | Zero line만 | **Zero line + Mean bias** |
| 신뢰구간 | 없음 | **95% 신뢰구간** |
| 평균 residual | 표시 안함 | **텍스트로 명시** |
| 임상 해석 | 어려움 | **직관적이고 명확** |

## 실제 적용 (1:N 예측)

1. **딥러닝 모델 예측값 획득**
2. **정확한 Velocity 측정** (거리 측정 센서 사용)
3. **멀티 아웃풋 모델로 모든 parameters 동시 예측**
4. **원본 예측값과 보정값 결합** (가중 평균)

## 주의사항

- AutoML 학습 시간은 `automl_time_budget` 파라미터로 조절 가능
- 데이터 크기에 따라 메모리 사용량이 클 수 있음
- 첫 실행 시 FLAML 패키지 다운로드 시간 필요

## 문제 해결

### FLAML 설치 오류
```bash
pip install flaml --upgrade
```

### Auto-sklearn 사용 (대안)
```bash
pip install auto-sklearn
```
코드에서 자동으로 사용 가능한 AutoML 라이브러리를 선택합니다.

## 향후 개선사항

1. **다중 입력 특성**: Velocity 외 추가 특성 활용
2. **시계열 모델**: 시간적 연속성을 고려한 모델
3. **개인화**: 개인별 특성을 반영한 보정 모델
4. **실시간 보정**: 스트리밍 데이터에 대한 실시간 보정

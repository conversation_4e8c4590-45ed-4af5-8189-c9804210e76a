#%%
import numpy as np
import pandas as pd

df = pd.read_excel('/datasets/gait/SingleView/gaitrite_full_dataset.xlsx')
df.head()
# %%
target_columns = [
        'Velocity', 
        'Stride_Len_L', 'Stride_Len_R',
        'StrideTm_L', 'StrideTm_R',
        'Swing_Perc_L', 'Swing_Perc_R', 
        '<PERSON><PERSON>_Perc_L', '<PERSON>ce_Perc_R',
        'D_Supp_PercL', 'D_Supp_PercR', 
    ]

df = df[target_columns]
df.hist(figsize=(20,20), bins=100)


#%%

#%%
import seaborn as sns
sns.pairplot(df)

# %%
# draw histogram using plotly
df.describe().iloc[1:, :]

#%%
# velocity를 제외한 모든 변수 쌍에 대해 비율 예측 및 원본 스케일 복원 후 성능 평가
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score, mean_squared_error

df = df.sample(3000)

cols = [c for c in df.columns if c != "Velocity"]
results = []

for i in range(len(cols)):
    for j in range(i+1, len(cols)):
        col1, col2 = cols[i], cols[j]
        # 비율 계산
        ratio = df[col1] / df[col2]
        # Velocity로 col1 예측
        reg1 = LinearRegression().fit(df[["Velocity"]], df[col1])
        col1_pred = reg1.predict(df[["Velocity"]])
        # Velocity로 비율 예측
        reg_ratio = LinearRegression().fit(df[["Velocity"]], ratio)
        ratio_pred = reg_ratio.predict(df[["Velocity"]])
        # col2 예측 (복원)
        col2_pred = col1_pred / ratio_pred
        # 성능 평가
        r2_1 = r2_score(df[col1], col1_pred)
        r2_2 = r2_score(df[col2], col2_pred)
        mse_1 = mean_squared_error(df[col1], col1_pred)
        mse_2 = mean_squared_error(df[col2], col2_pred)
        results.append({
            "col1": col1, "col2": col2,
            "r2_col1": r2_1, "mse_col1": mse_1,
            "r2_col2": r2_2, "mse_col2": mse_2
        })

for res in results:
    print(f"{res['col1']} (direct): R2={res['r2_col1']:.3f}, MSE={res['mse_col1']:.3f}")
    print(f"{res['col2']} (from ratio): R2={res['r2_col2']:.3f}, MSE={res['mse_col2']:.3f}")
    print('-'*40)



# %%
# g = sns.pairplot(df, diag_kind="kde")
# g.map_lower(sns.kdeplot, levels=4, color=".2")
# # %%


#%%
results
# %%

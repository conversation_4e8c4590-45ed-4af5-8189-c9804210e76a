"""
Gait Parameter Calibration 유틸리티 함수들
"""

import numpy as np
import pandas as pd
import pickle
import os
from typing import Dict, List, Union, Tuple
import matplotlib.pyplot as plt
import seaborn as sns

class GaitCalibrationPredictor:
    """
    학습된 모델을 사용하여 실시간 예측을 수행하는 클래스
    """
    
    def __init__(self, models_dir='./models'):
        """
        Args:
            models_dir (str): 저장된 모델들이 있는 디렉토리
        """
        self.models_dir = models_dir
        self.multi_output_model = None
        self.velocity_scaler = None
        self.target_columns = None
        self.load_models()
    
    def load_models(self):
        """
        저장된 멀티 아웃풋 모델과 스케일러를 로드
        """
        if not os.path.exists(self.models_dir):
            raise FileNotFoundError(f"Models directory {self.models_dir} not found. Please train models first.")

        # 앙상블 모델 우선 확인
        ensemble_path = os.path.join(self.models_dir, 'ensemble_model.pkl')
        single_path = os.path.join(self.models_dir, 'multi_output_model.pkl')
        scaler_path = os.path.join(self.models_dir, 'velocity_scaler.pkl')
        target_info_path = os.path.join(self.models_dir, 'target_columns.pkl')

        # 공통 파일 확인
        if not all(os.path.exists(path) for path in [scaler_path, target_info_path]):
            raise FileNotFoundError("Required model files (scaler, target_columns) not found.")

        if os.path.exists(ensemble_path):
            # 앙상블 모델 로드
            with open(ensemble_path, 'rb') as f:
                self.multi_output_model = pickle.load(f)

            # 앙상블 정보 로드
            ensemble_info_path = os.path.join(self.models_dir, 'ensemble_info.pkl')
            if os.path.exists(ensemble_info_path):
                with open(ensemble_info_path, 'rb') as f:
                    self.ensemble_info = pickle.load(f)
            else:
                self.ensemble_info = {'method': 'unknown', 'n_components': 'unknown'}

            self.is_ensemble = True
            print(f"🤖 Ensemble model loaded from {self.models_dir}")
            print(f"   Method: {self.ensemble_info['method']}")
            print(f"   Components: {self.ensemble_info['n_components']}")

        elif os.path.exists(single_path):
            # 단일 멀티 아웃풋 모델 로드
            with open(single_path, 'rb') as f:
                self.multi_output_model = pickle.load(f)

            self.is_ensemble = False
            print(f"📊 Single multi-output model loaded from {self.models_dir}")
        else:
            raise FileNotFoundError("No trained model found. Please train a model first.")

        # 스케일러 로드
        with open(scaler_path, 'rb') as f:
            self.velocity_scaler = pickle.load(f)

        # Target columns 정보 로드
        with open(target_info_path, 'rb') as f:
            self.target_columns = pickle.load(f)

        print(f"Loaded multi-output model from {self.models_dir}")
        print(f"Target parameters: {len(self.target_columns)} parameters")
    
    def predict_single(self, velocity: float) -> Dict[str, float]:
        """
        단일 velocity 값에 대한 단일 멀티 아웃풋 모델 예측

        Args:
            velocity (float): Velocity 값 (cm/s)

        Returns:
            Dict[str, float]: 각 parameter별 예측값
        """
        velocity_array = np.array([[velocity]])

        # 스케일링 적용
        velocity_scaled = self.velocity_scaler.transform(velocity_array)

        # 단일 멀티 아웃풋 모델로 예측
        try:
            predictions_array = self.multi_output_model.predict(velocity_scaled)[0]
        except Exception as e:
            raise RuntimeError(f"Prediction failed: {e}")

        # 결과를 딕셔너리로 변환
        predictions = {}
        for i, target in enumerate(self.target_columns):
            predictions[target] = float(predictions_array[i])

        return predictions
    
    def predict_batch(self, velocities: List[float]) -> pd.DataFrame:
        """
        여러 velocity 값들에 대한 배치 예측
        
        Args:
            velocities (List[float]): Velocity 값들의 리스트
            
        Returns:
            pd.DataFrame: 예측 결과 데이터프레임
        """
        results = []
        
        for vel in velocities:
            pred = self.predict_single(vel)
            pred['Velocity'] = vel
            results.append(pred)
        
        df = pd.DataFrame(results)
        # Velocity 컬럼을 첫 번째로 이동
        cols = ['Velocity'] + [col for col in df.columns if col != 'Velocity']
        return df[cols]
    
    def calibrate_deep_learning_output(self,
                                     dl_predictions: Dict[str, float],
                                     measured_velocity: float,
                                     calibration_weight: float = 0.5) -> Dict[str, float]:
        """
        딥러닝 모델의 출력을 velocity 기반 멀티 아웃풋 예측으로 보정

        Args:
            dl_predictions (Dict[str, float]): 딥러닝 모델의 예측값들
            measured_velocity (float): 정확하게 측정된 velocity 값
            calibration_weight (float): 보정 가중치 (0: 원본 유지, 1: 완전 보정)

        Returns:
            Dict[str, float]: 보정된 예측값들
        """
        # Velocity 기반 멀티 아웃풋 예측값 획득
        velocity_predictions = self.predict_single(measured_velocity)

        # 가중 평균으로 보정
        calibrated = {}
        for param in self.target_columns:
            if param in dl_predictions:
                original = dl_predictions[param]
                velocity_based = velocity_predictions[param]
                calibrated[param] = (1 - calibration_weight) * original + calibration_weight * velocity_based
            else:
                # 딥러닝 예측에 없는 parameter는 velocity 기반 예측값 사용
                calibrated[param] = velocity_predictions[param]

        # 측정된 velocity 추가
        calibrated['Velocity'] = measured_velocity

        return calibrated

    def postprocess_model_output(self,
                                model_predictions: Dict[str, float],
                                measured_velocity: float = None,
                                model_velocity_range: Tuple[float, float] = (30.0, 150.0),
                                use_velocity_constraint: bool = True,
                                velocity_tolerance: float = 10.0) -> Dict[str, float]:
        """
        모델 출력을 거리 센서 기반 속도 범위 벗어남 정도에 따라 ML 모델로 보정하는 후처리 메서드

        Args:
            model_predictions (Dict[str, float]): 원본 모델의 예측값들
            measured_velocity (float): 거리 센서로 측정된 실제 속도 (cm/s)
            model_velocity_range (Tuple[float, float]): 모델이 인식 가능한 속도 범위 (min, max)
            use_velocity_constraint (bool): Velocity 제약 조건 사용 여부
            velocity_tolerance (float): Velocity 허용 오차 범위 (cm/s)

        Returns:
            Dict[str, float]: 보정된 예측값들
        """
        # 1. 입력 검증
        if not model_predictions:
            raise ValueError("Model predictions cannot be empty")

        # 2. 속도 정보 확인 및 추정
        predicted_velocity = model_predictions.get('Velocity', None)
        if predicted_velocity is None:
            # Velocity가 없으면 Stride Length로 추정
            stride_len_l = model_predictions.get('Stride_Len_L', 0)
            stride_len_r = model_predictions.get('Stride_Len_R', 0)
            if stride_len_l > 0 and stride_len_r > 0:
                # 평균 stride length로 velocity 추정 (경험적 공식)
                avg_stride_len = (stride_len_l + stride_len_r) / 2
                predicted_velocity = avg_stride_len * 0.85  # 경험적 계수
            else:
                predicted_velocity = 80.0  # 기본값

        # 3. 거리 센서 속도 사용 (우선순위: 측정값 > 예측값)
        reference_velocity = measured_velocity if measured_velocity is not None else predicted_velocity

        # 4. 속도 범위 벗어남 정도에 따른 보정 강도 계산
        # correction_weight = self._calculate_velocity_based_correction_weight(
        #     reference_velocity, model_velocity_range
        # )
        
        correction_weight = 0.5
        
        # 5. ML 모델 기반 참조 예측값 생성
        ml_reference = self.predict_single(reference_velocity)

        # 6. 각 parameter별 보정 수행
        corrected_predictions = {}
        correction_info = {}

        for param in self.target_columns:
            if param in model_predictions:
                original_value = model_predictions[param]
                ml_reference_value = ml_reference[param]

                # 속도 기반 가중 평균
                # correction_weight가 높을수록 ML 참조값을 더 많이 반영
                corrected_value = ((1.0 - correction_weight) * original_value +
                                 correction_weight * ml_reference_value)

                # 7. 물리적 제약 조건 적용
                corrected_value = self._apply_physical_constraints(param, corrected_value)

                corrected_predictions[param] = corrected_value
                correction_info[param] = {
                    'original': original_value,
                    'ml_reference': ml_reference_value,
                    'velocity_correction_weight': correction_weight,
                    'correction_applied': abs(corrected_value - original_value) > 0.01,
                    'correction_magnitude': abs(corrected_value - original_value)
                }
            else:
                # 누락된 parameter는 ML 모델로 보완
                corrected_predictions[param] = ml_reference[param]
                correction_info[param] = {
                    'original': None,
                    'ml_reference': ml_reference[param],
                    'velocity_correction_weight': 1.0,  # 완전히 ML 기반
                    'correction_applied': True,
                    'correction_magnitude': 0.0
                }

        # 8. Velocity 제약 조건 적용
        if use_velocity_constraint and reference_velocity is not None:
            corrected_predictions = self._apply_velocity_constraints(
                corrected_predictions, reference_velocity, velocity_tolerance
            )

        # 9. 최종 결과에 메타데이터 추가
        corrected_predictions['Velocity'] = reference_velocity
        corrected_predictions['_correction_info'] = correction_info
        corrected_predictions['_ml_reference_velocity'] = reference_velocity
        corrected_predictions['_measured_velocity'] = measured_velocity
        corrected_predictions['_predicted_velocity'] = predicted_velocity
        corrected_predictions['_velocity_correction_weight'] = correction_weight
        corrected_predictions['_model_velocity_range'] = model_velocity_range

        return corrected_predictions

    def _calculate_velocity_based_correction_weight(self,
                                                  velocity: float,
                                                  model_range: Tuple[float, float]) -> float:
        """
        속도가 모델 인식 범위를 벗어나는 정도에 따라 보정 가중치 계산

        Args:
            velocity (float): 참조 속도 (거리 센서 측정값 또는 예측값)
            model_range (Tuple[float, float]): 모델이 인식 가능한 속도 범위 (min, max)

        Returns:
            float: 보정 가중치 (0.0: 원본 유지, 1.0: 완전 보정)
        """
        min_vel, max_vel = model_range

        # 모델 범위 내에 있는 경우
        if min_vel <= velocity <= max_vel:
            # 범위 중앙에 가까울수록 보정 강도 감소
            range_center = (min_vel + max_vel) / 2
            range_width = max_vel - min_vel

            # 중앙으로부터의 거리 비율 (0~0.5)
            distance_ratio = abs(velocity - range_center) / (range_width / 2)

            # 중앙에서는 0.1, 경계에서는 0.3의 보정 가중치
            return 0.1 + 0.2 * distance_ratio

        # 모델 범위를 벗어난 경우
        else:
            if velocity < min_vel:
                # 하한선 아래
                deviation = min_vel - velocity
                max_deviation = min_vel * 0.5  # 하한선의 50%까지를 최대 편차로 설정
            else:
                # 상한선 위
                deviation = velocity - max_vel
                max_deviation = max_vel * 0.5  # 상한선의 50%까지를 최대 편차로 설정

            # 편차 비율에 따라 보정 강도 결정 (0.3 ~ 0.9)
            deviation_ratio = min(deviation / max_deviation, 1.0)
            return 0.3 + 0.6 * deviation_ratio

    def _apply_physical_constraints(self, param: str, value: float) -> float:
        """
        물리적 제약 조건을 적용하여 비현실적인 값을 보정

        Args:
            param (str): Parameter 이름
            value (float): 원본 값

        Returns:
            float: 제약 조건이 적용된 값
        """
        constraints = {
            'Stride_Len_L': (10.0, 200.0),    # 보폭 길이: 10-200cm
            'Stride_Len_R': (10.0, 200.0),
            'Swing_Perc_L': (20.0, 50.0),     # 스윙 비율: 20-50%
            'Swing_Perc_R': (20.0, 50.0),
            'Stance_Perc_L': (50.0, 80.0),    # 지지 비율: 50-80%
            'Stance_Perc_R': (50.0, 80.0),
            'D_Supp_PercL': (5.0, 60.0),      # 이중지지: 5-60%
            'D_Supp_PercR': (5.0, 60.0),
            'Velocity': (5.0, 200.0)          # 속도: 5-200 cm/s
        }

        if param in constraints:
            min_val, max_val = constraints[param]
            return np.clip(value, min_val, max_val)

        return value

    def _apply_velocity_constraints(self,
                                  predictions: Dict[str, float],
                                  target_velocity: float,
                                  tolerance: float) -> Dict[str, float]:
        """
        Velocity 제약 조건을 적용하여 일관성 있는 예측값 생성

        Args:
            predictions (Dict[str, float]): 예측값들
            target_velocity (float): 목표 velocity
            tolerance (float): 허용 오차

        Returns:
            Dict[str, float]: 제약 조건이 적용된 예측값들
        """
        # Stride Length와 Velocity 간의 일관성 확인
        stride_len_l = predictions.get('Stride_Len_L', 0)
        stride_len_r = predictions.get('Stride_Len_R', 0)

        if stride_len_l > 0 and stride_len_r > 0:
            avg_stride_len = (stride_len_l + stride_len_r) / 2
            implied_velocity = avg_stride_len * 0.85  # 경험적 계수

            # Velocity 불일치가 허용 범위를 벗어나면 보정
            if abs(implied_velocity - target_velocity) > tolerance:
                # Stride Length를 target velocity에 맞게 조정
                correction_factor = target_velocity / implied_velocity
                predictions['Stride_Len_L'] *= correction_factor
                predictions['Stride_Len_R'] *= correction_factor

        # Swing과 Stance 비율의 합이 100%가 되도록 조정
        for side in ['L', 'R']:
            swing_key = f'Swing_Perc_{side}'
            stance_key = f'Stance_Perc_{side}'

            if swing_key in predictions and stance_key in predictions:
                swing_val = predictions[swing_key]
                stance_val = predictions[stance_key]
                total = swing_val + stance_val

                if abs(total - 100.0) > 1.0:  # 1% 허용 오차
                    # 비율을 정규화
                    predictions[swing_key] = (swing_val / total) * 100.0
                    predictions[stance_key] = (stance_val / total) * 100.0

        return predictions

def test_postprocessing_system(predictor: GaitCalibrationPredictor,
                              test_cases: List[Dict] = None) -> pd.DataFrame:
    """
    후처리 시스템 테스트 함수

    Args:
        predictor (GaitCalibrationPredictor): 예측기 인스턴스
        test_cases (List[Dict]): 테스트 케이스들

    Returns:
        pd.DataFrame: 테스트 결과
    """
    if test_cases is None:
        # 기본 테스트 케이스들 (부정확한 예측값들 시뮬레이션)
        test_cases = [
            {
                'name': 'In_Range_Normal',
                'predictions': {
                    'Velocity': 85.0,  # 모델 범위 내 (30-150)
                    'Stride_Len_L': 88.5,
                    'Stride_Len_R': 87.2,
                    'Swing_Perc_L': 35.8,
                    'Swing_Perc_R': 36.1,
                    'Stance_Perc_L': 64.2,
                    'Stance_Perc_R': 63.9,
                    'D_Supp_PercL': 28.0,
                    'D_Supp_PercR': 28.5
                },
                'measured_velocity': 87.0,  # 거리 센서 측정값 (약간 다름)
                'model_range': (30.0, 150.0)
            },
            {
                'name': 'Out_Of_Range_Low',
                'predictions': {
                    'Velocity': 25.0,  # 모델 범위 밖 (하한선 아래)
                    'Stride_Len_L': 35.0,  # 부정확 (너무 작음)
                    'Stride_Len_R': 33.0,
                    'Swing_Perc_L': 32.0,
                    'Swing_Perc_R': 31.5,
                    'Stance_Perc_L': 68.0,
                    'Stance_Perc_R': 68.5,
                    'D_Supp_PercL': 35.0,
                    'D_Supp_PercR': 36.0
                },
                'measured_velocity': 20.0,  # 거리 센서: 더 낮음 (강한 보정 필요)
                'model_range': (30.0, 150.0)
            },
            {
                'name': 'Out_Of_Range_High',
                'predictions': {
                    'Velocity': 180.0,  # 모델 범위 밖 (상한선 위)
                    'Stride_Len_L': 165.0,  # 부정확 (너무 큼)
                    'Stride_Len_R': 170.0,
                    'Swing_Perc_L': 42.0,
                    'Swing_Perc_R': 43.0,
                    'Stance_Perc_L': 58.0,
                    'Stance_Perc_R': 57.0,
                    'D_Supp_PercL': 15.0,
                    'D_Supp_PercR': 14.0
                },
                'measured_velocity': 200.0,  # 거리 센서: 더 높음 (강한 보정 필요)
                'model_range': (30.0, 150.0)
            },
            {
                'name': 'Missing_Parameters',
                'predictions': {
                    'Stride_Len_L': 78.5,
                    'Stride_Len_R': 79.1,
                    # Velocity 및 기타 parameters 누락
                },
                'measured_velocity': 75.0,  # 거리 센서로만 속도 제공
                'model_range': (30.0, 150.0)
            }
        ]

    results = []

    print("🧪 Testing Postprocessing System")
    print("=" * 50)

    for i, test_case in enumerate(test_cases):
        print(f"\n📋 Test Case {i+1}: {test_case['name']}")

        # 후처리 실행
        corrected = predictor.postprocess_model_output(
            model_predictions=test_case['predictions'],
            measured_velocity=test_case.get('measured_velocity', None),
            model_velocity_range=test_case.get('model_range', (30.0, 150.0)),
            use_velocity_constraint=True,
            velocity_tolerance=10.0
        )

        # 보정 정보 추출
        correction_info = corrected.pop('_correction_info', {})
        ml_ref_velocity = corrected.pop('_ml_reference_velocity', None)

        # 결과 분석
        for param in predictor.target_columns:
            original_val = test_case['predictions'].get(param, None)
            corrected_val = corrected.get(param, None)

            if param in correction_info:
                info = correction_info[param]
                results.append({
                    'test_case': test_case['name'],
                    'parameter': param,
                    'original_value': original_val,
                    'corrected_value': corrected_val,
                    'ml_reference': info.get('ml_reference', None),
                    'velocity_correction_weight': info.get('velocity_correction_weight', None),
                    'correction_applied': info.get('correction_applied', False),
                    'correction_magnitude': info.get('correction_magnitude', 0.0),
                    'measured_velocity': test_case.get('measured_velocity', None),
                    'predicted_velocity': corrected.get('_predicted_velocity', None),
                    'ml_reference_velocity': ml_ref_velocity
                })

        # 요약 출력
        corrections_applied = sum(1 for info in correction_info.values()
                                if info.get('correction_applied', False))
        print(f"  ✅ Corrections applied: {corrections_applied}/{len(correction_info)}")

        # 주요 보정 사항 출력
        for param, info in correction_info.items():
            if info.get('correction_applied', False) and info.get('correction_magnitude', 0) > 1.0:
                print(f"    🔧 {param}: {info['original']:.1f} → {corrected[param]:.1f} "
                      f"(Δ={info['correction_magnitude']:.1f})")

    return pd.DataFrame(results)

def demo_postprocessing_workflow():
    """
    후처리 시스템 사용법 데모
    """
    print("🎯 후처리 시스템 사용법 데모")
    print("=" * 50)

    try:
        # 1. 예측기 초기화
        predictor = GaitCalibrationPredictor()
        print("✅ ML 보정 모델 로드 완료")

        # 2. 시뮬레이션: 부정확한 모델 출력 (모델 범위 밖)
        print("\n📊 시나리오: 모델 인식 범위를 벗어난 고속 상황")
        noisy_predictions = {
            'Velocity': 180.0,      # 모델 범위(30-150) 밖
            'Stride_Len_L': 165.0,  # 과대 예측 (실제보다 큼)
            'Stride_Len_R': 158.2,  # 과대 예측
            'Swing_Perc_L': 42.8,   # 부정확
            'Swing_Perc_R': 41.1,   # 부정확
            'Stance_Perc_L': 57.2,  # 부정확 (Swing과 합이 100%가 아님)
            'Stance_Perc_R': 58.9,  # 부정확
            'D_Supp_PercL': 16.5,   # 부정확
            'D_Supp_PercR': 17.8    # 부정확
        }

        # 3. 거리 센서로 측정된 실제 속도
        measured_velocity = 195.0  # 모델 예측보다도 더 빠름
        model_velocity_range = (30.0, 150.0)  # 모델 학습 범위

        print("원본 예측값:")
        for param, value in noisy_predictions.items():
            print(f"  {param:15s}: {value:6.1f}")

        print(f"\n거리 센서 측정 속도: {measured_velocity} cm/s")
        print(f"모델 인식 범위: {model_velocity_range[0]}-{model_velocity_range[1]} cm/s")

        # 속도 범위 벗어남 정도 계산
        range_deviation = measured_velocity - model_velocity_range[1]
        print(f"범위 초과: +{range_deviation} cm/s → 강한 보정 필요")

        # 4. 속도 기반 후처리 적용
        print("\n🔧 속도 기반 ML 후처리 적용...")
        corrected_predictions = predictor.postprocess_model_output(
            model_predictions=noisy_predictions,
            measured_velocity=measured_velocity,
            model_velocity_range=model_velocity_range,
            use_velocity_constraint=True,
            velocity_tolerance=8.0
        )

        # 5. 결과 분석
        correction_info = corrected_predictions.pop('_correction_info', {})

        print("\n✅ 보정된 예측값:")
        total_corrections = 0
        total_magnitude = 0.0

        for param in predictor.target_columns:
            original = noisy_predictions.get(param, 'N/A')
            corrected = corrected_predictions.get(param, 'N/A')

            if param in correction_info:
                info = correction_info[param]
                magnitude = info.get('correction_magnitude', 0.0)
                applied = info.get('correction_applied', False)
                weight = info.get('velocity_correction_weight', 0.0)

                if applied and magnitude > 0.1:
                    total_corrections += 1
                    total_magnitude += magnitude
                    status = f"🔧 (Δ={magnitude:.1f}, W={weight:.2f})"
                else:
                    status = "✓"

                print(f"  {param:15s}: {original:6.1f} → {corrected:6.1f} {status}")

        # 6. 요약 통계
        velocity_weight = corrected_predictions.get('_velocity_correction_weight', 0.0)
        print(f"\n📈 보정 요약:")
        print(f"  속도 기반 보정 가중치: {velocity_weight:.3f}")
        print(f"  보정 적용된 parameters: {total_corrections}/{len(predictor.target_columns)}")
        print(f"  평균 보정 크기: {total_magnitude/max(total_corrections, 1):.2f}")
        print(f"  속도 범위 초과 정도: {range_deviation:.1f} cm/s")

        # 7. 물리적 일관성 검증
        print(f"\n🔍 물리적 일관성 검증:")
        for side in ['L', 'R']:
            swing = corrected_predictions.get(f'Swing_Perc_{side}', 0)
            stance = corrected_predictions.get(f'Stance_Perc_{side}', 0)
            total_percent = swing + stance
            print(f"  {side}측 Swing+Stance: {total_percent:.1f}% {'✅' if abs(total_percent-100) < 1 else '❌'}")

        return corrected_predictions

    except Exception as e:
        print(f"❌ 오류 발생: {e}")
        return None

def analyze_parameter_relationships(df: pd.DataFrame, save_plots: bool = True) -> Dict:
    """
    Gait parameters 간의 관계 분석
    
    Args:
        df (pd.DataFrame): Gait 데이터
        save_plots (bool): 플롯 저장 여부
        
    Returns:
        Dict: 분석 결과
    """
    # 상관관계 매트릭스
    corr_matrix = df.corr()
    
    # Velocity와의 상관관계
    velocity_corr = corr_matrix['Velocity'].drop('Velocity').sort_values(key=abs, ascending=False)
    
    # 시각화
    if save_plots:
        # 상관관계 히트맵
        plt.figure(figsize=(12, 10))
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                   square=True, fmt='.3f')
        plt.title('Gait Parameters Correlation Matrix')
        plt.tight_layout()
        plt.savefig('correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # Velocity 상관관계 바 플롯
        plt.figure(figsize=(10, 6))
        velocity_corr.plot(kind='barh')
        plt.title('Correlation with Velocity')
        plt.xlabel('Correlation Coefficient')
        plt.tight_layout()
        plt.savefig('velocity_correlation_bar.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    return {
        'correlation_matrix': corr_matrix,
        'velocity_correlations': velocity_corr,
        'strongest_velocity_correlation': velocity_corr.iloc[0],
        'weakest_velocity_correlation': velocity_corr.iloc[-1]
    }

def evaluate_calibration_performance(original_predictions: pd.DataFrame,
                                   calibrated_predictions: pd.DataFrame,
                                   ground_truth: pd.DataFrame) -> pd.DataFrame:
    """
    보정 전후 성능 비교
    
    Args:
        original_predictions (pd.DataFrame): 원본 예측값들
        calibrated_predictions (pd.DataFrame): 보정된 예측값들
        ground_truth (pd.DataFrame): 실제값들
        
    Returns:
        pd.DataFrame: 성능 비교 결과
    """
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
    
    results = []
    
    # 공통 컬럼 찾기
    common_cols = set(original_predictions.columns) & set(calibrated_predictions.columns) & set(ground_truth.columns)
    common_cols.discard('Velocity')  # Velocity는 제외
    
    for col in common_cols:
        # 원본 성능
        orig_mse = mean_squared_error(ground_truth[col], original_predictions[col])
        orig_mae = mean_absolute_error(ground_truth[col], original_predictions[col])
        orig_r2 = r2_score(ground_truth[col], original_predictions[col])
        
        # 보정 후 성능
        cal_mse = mean_squared_error(ground_truth[col], calibrated_predictions[col])
        cal_mae = mean_absolute_error(ground_truth[col], calibrated_predictions[col])
        cal_r2 = r2_score(ground_truth[col], calibrated_predictions[col])
        
        # 개선율 계산
        mse_improvement = (orig_mse - cal_mse) / orig_mse * 100
        mae_improvement = (orig_mae - cal_mae) / orig_mae * 100
        r2_improvement = (cal_r2 - orig_r2) / abs(orig_r2) * 100 if orig_r2 != 0 else 0
        
        results.append({
            'Parameter': col,
            'Original_MSE': orig_mse,
            'Calibrated_MSE': cal_mse,
            'MSE_Improvement_%': mse_improvement,
            'Original_MAE': orig_mae,
            'Calibrated_MAE': cal_mae,
            'MAE_Improvement_%': mae_improvement,
            'Original_R2': orig_r2,
            'Calibrated_R2': cal_r2,
            'R2_Improvement_%': r2_improvement
        })
    
    return pd.DataFrame(results)

def create_calibration_report(predictor: GaitCalibrationPredictor,
                            test_velocities: List[float] = None) -> str:
    """
    보정 시스템에 대한 종합 리포트 생성
    
    Args:
        predictor (GaitCalibrationPredictor): 예측기 인스턴스
        test_velocities (List[float]): 테스트할 velocity 값들
        
    Returns:
        str: 리포트 텍스트
    """
    if test_velocities is None:
        test_velocities = [0.5, 0.8, 1.0, 1.2, 1.5, 1.8, 2.0]
    
    report = []
    report.append("=" * 60)
    report.append("GAIT PARAMETER CALIBRATION SYSTEM REPORT")
    report.append("=" * 60)
    
    # 시스템 정보
    report.append(f"\nMulti-Output Model: {'Loaded' if predictor.multi_output_model else 'Not loaded'}")
    report.append(f"Target Parameters: {len(predictor.target_columns)}")
    report.append(f"Models Directory: {predictor.models_dir}")
    
    # 예측 예시
    report.append("\n" + "-" * 40)
    report.append("PREDICTION EXAMPLES")
    report.append("-" * 40)
    
    for vel in test_velocities:
        predictions = predictor.predict_single(vel)
        report.append(f"\nVelocity: {vel:.1f} m/s")
        for param, value in predictions.items():
            report.append(f"  {param:15s}: {value:8.3f}")
    
    # 민감도 분석
    report.append("\n" + "-" * 40)
    report.append("SENSITIVITY ANALYSIS")
    report.append("-" * 40)
    
    # 속도 변화에 따른 각 parameter의 변화율 계산
    base_vel = 1.0
    delta_vel = 0.1
    
    base_pred = predictor.predict_single(base_vel)
    high_pred = predictor.predict_single(base_vel + delta_vel)
    
    report.append(f"\nSensitivity (change per {delta_vel} m/s velocity increase):")
    for param in predictor.target_columns:
        if param in base_pred and param in high_pred:
            sensitivity = (high_pred[param] - base_pred[param]) / delta_vel
            report.append(f"  {param:15s}: {sensitivity:8.4f}")
    
    return "\n".join(report)

def save_calibration_config(predictor: GaitCalibrationPredictor,
                          config_path: str = 'calibration_config.json'):
    """
    보정 시스템 설정을 JSON 파일로 저장
    """
    import json

    config = {
        'models_directory': predictor.models_dir,
        'target_parameters': predictor.target_columns,
        'model_type': 'multi_output',
        'model_loaded': predictor.multi_output_model is not None,
        'creation_timestamp': pd.Timestamp.now().isoformat()
    }

    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)

    print(f"Configuration saved to {config_path}")

def create_interactive_scatter_plot(predictor: GaitCalibrationPredictor,
                                  n_points: int = 100,
                                  velocity_range: Tuple[float, float] = (30, 150)):
    """
    인터랙티브 scatter plot 생성 (Plotly 사용)

    Args:
        predictor: GaitCalibrationPredictor 인스턴스
        n_points: 생성할 점의 개수
        velocity_range: Velocity 범위 (min, max)
    """
    try:
        import plotly.graph_objects as go
        from plotly.subplots import make_subplots
        import plotly.express as px
    except ImportError:
        print("Plotly not installed. Install with: pip install plotly")
        return

    # Velocity 값들 생성
    velocity_values = np.linspace(velocity_range[0], velocity_range[1], n_points)

    # 배치 예측
    batch_predictions = predictor.predict_batch(velocity_values.tolist())

    # 서브플롯 생성
    fig = make_subplots(
        rows=3, cols=4,
        subplot_titles=predictor.target_columns,
        specs=[[{"secondary_y": False}]*4 for _ in range(3)]
    )

    # 각 parameter별 scatter plot 추가
    for i, target in enumerate(predictor.target_columns):
        row = i // 4 + 1
        col = i % 4 + 1

        pred_values = batch_predictions[target].values

        fig.add_trace(
            go.Scatter(
                x=velocity_values,
                y=pred_values,
                mode='markers',
                marker=dict(
                    size=8,
                    color=velocity_values,
                    colorscale='Viridis',
                    showscale=True if i == 0 else False,
                    colorbar=dict(title="Velocity (cm/s)") if i == 0 else None
                ),
                name=target,
                hovertemplate=f'<b>{target}</b><br>' +
                             'Velocity: %{x:.1f} cm/s<br>' +
                             f'{target}: %{y:.3f}<br>' +
                             '<extra></extra>'
            ),
            row=row, col=col
        )

        # X축과 Y축 레이블 설정
        fig.update_xaxes(title_text="Velocity (cm/s)", row=row, col=col)
        fig.update_yaxes(title_text=target, row=row, col=col)

    # 레이아웃 업데이트
    fig.update_layout(
        title="Interactive Gait Parameter Predictions vs Velocity",
        height=900,
        showlegend=False,
        hovermode='closest'
    )

    # HTML 파일로 저장
    fig.write_html("interactive_scatter_plot.html")
    print("Interactive scatter plot saved as 'interactive_scatter_plot.html'")

    # 브라우저에서 열기 (선택사항)
    try:
        fig.show()
    except:
        print("Could not open in browser. Please open 'interactive_scatter_plot.html' manually.")

    return fig

def create_correlation_heatmap(predictor: GaitCalibrationPredictor,
                             n_points: int = 200,
                             velocity_range: Tuple[float, float] = (30, 150)):
    """
    예측된 gait parameters 간의 상관관계 히트맵 생성

    Args:
        predictor: GaitCalibrationPredictor 인스턴스
        n_points: 생성할 점의 개수
        velocity_range: Velocity 범위 (min, max)
    """
    # Velocity 값들 생성
    velocity_values = np.linspace(velocity_range[0], velocity_range[1], n_points)

    # 배치 예측
    batch_predictions = predictor.predict_batch(velocity_values.tolist())

    # 상관관계 계산
    correlation_matrix = batch_predictions.corr()

    # 히트맵 생성
    plt.figure(figsize=(12, 10))
    mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))  # 상삼각 마스크

    sns.heatmap(correlation_matrix,
                mask=mask,
                annot=True,
                cmap='coolwarm',
                center=0,
                square=True,
                fmt='.3f',
                cbar_kws={"shrink": .8})

    plt.title('Correlation Matrix of Predicted Gait Parameters', fontsize=16, fontweight='bold')
    plt.tight_layout()
    plt.savefig('predicted_parameters_correlation.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("Correlation heatmap saved as 'predicted_parameters_correlation.png'")

    # 강한 상관관계 출력
    print("\n=== Strong Correlations (|r| > 0.8) ===")
    strong_corr = []
    for i in range(len(correlation_matrix.columns)):
        for j in range(i+1, len(correlation_matrix.columns)):
            corr_val = correlation_matrix.iloc[i, j]
            if abs(corr_val) > 0.8:
                param1 = correlation_matrix.columns[i]
                param2 = correlation_matrix.columns[j]
                strong_corr.append((param1, param2, corr_val))

    if strong_corr:
        for param1, param2, corr_val in sorted(strong_corr, key=lambda x: abs(x[2]), reverse=True):
            print(f"  {param1:15s} ↔ {param2:15s}: {corr_val:6.3f}")
    else:
        print("  No strong correlations found (|r| > 0.8)")

    return correlation_matrix

def quick_validation_summary(predictor: GaitCalibrationPredictor,
                            test_velocities: List[float] = None,
                            ground_truth_data: pd.DataFrame = None) -> Dict:
    """
    빠른 validation 요약 (실제 데이터가 있는 경우)

    Args:
        predictor: GaitCalibrationPredictor 인스턴스
        test_velocities: 테스트할 velocity 값들
        ground_truth_data: 실제 데이터 (선택사항)

    Returns:
        Dict: Validation 결과 요약
    """
    if test_velocities is None:
        test_velocities = [50, 70, 90, 110, 130]  # cm/s

    # 예측 수행
    predictions = []
    for vel in test_velocities:
        pred = predictor.predict_single(vel)
        pred['Velocity'] = vel
        predictions.append(pred)

    predictions_df = pd.DataFrame(predictions)

    print("🎯 Quick Validation Summary")
    print("=" * 50)
    print(f"Test velocities: {test_velocities}")
    print(f"Predictions generated for {len(predictor.target_columns)} parameters")

    # 예측값 범위 출력
    print("\n📊 Prediction Ranges:")
    for param in predictor.target_columns:
        values = predictions_df[param].values
        print(f"  {param:15s}: {values.min():8.3f} - {values.max():8.3f}")

    # Ground-truth와 비교 (있는 경우)
    if ground_truth_data is not None:
        print("\n🔍 Ground-Truth Comparison:")
        # 가장 가까운 velocity 값들과 비교
        for vel in test_velocities:
            closest_idx = (ground_truth_data['Velocity'] - vel).abs().idxmin()
            gt_row = ground_truth_data.loc[closest_idx]
            pred_row = predictions_df[predictions_df['Velocity'] == vel].iloc[0]

            print(f"\n  Velocity {vel} cm/s (closest GT: {gt_row['Velocity']:.1f}):")
            for param in predictor.target_columns:
                if param in gt_row:
                    gt_val = gt_row[param]
                    pred_val = pred_row[param]
                    error = abs(gt_val - pred_val)
                    error_pct = (error / abs(gt_val)) * 100 if gt_val != 0 else 0
                    print(f"    {param:15s}: GT={gt_val:7.3f}, Pred={pred_val:7.3f}, Error={error:6.3f} ({error_pct:5.1f}%)")

    return {
        'test_velocities': test_velocities,
        'predictions': predictions_df,
        'target_parameters': predictor.target_columns
    }

# 사용 예시
if __name__ == "__main__":
    # 예측기 초기화 (모델이 학습되어 있다고 가정)
    try:
        predictor = GaitCalibrationPredictor()
        
        # 단일 예측
        velocity = 1.2
        prediction = predictor.predict_single(velocity)
        print(f"Prediction for velocity {velocity} m/s:")
        for param, value in prediction.items():
            print(f"  {param}: {value:.3f}")
        
        # 배치 예측
        velocities = [0.8, 1.0, 1.2, 1.5]
        batch_predictions = predictor.predict_batch(velocities)
        print("\nBatch predictions:")
        print(batch_predictions)
        
        # 리포트 생성
        report = create_calibration_report(predictor)
        print("\n" + report)
        
        # 설정 저장
        save_calibration_config(predictor)
        
    except FileNotFoundError:
        print("Models not found. Please train models first using:")
        print("python run_calibration.py full")

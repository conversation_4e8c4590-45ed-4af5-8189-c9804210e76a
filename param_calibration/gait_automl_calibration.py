"""
Gait Parameter Calibration using AutoML
========================================

이 코드는 Velocity 값을 기반으로 다른 gait parameters를 예측하는 AutoML 모델을 학습합니다.
정확한 Velocity 측정값을 활용하여 딥러닝 모델의 다른 gait parameter 예측을 보정하는 것이 목표입니다.
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# AutoML 라이브러리 import
try:
    from flaml import AutoML
    AUTOML_LIBRARY = 'flaml'
    print("Using FLAML for AutoML")
except ImportError:
    try:
        import autosklearn.regression
        AUTOML_LIBRARY = 'autosklearn'
        print("Using Auto-sklearn for AutoML")
    except ImportError:
        print("Neither FLAML nor Auto-sklearn is installed. Please install one of them:")
        print("pip install flaml")
        print("or")
        print("pip install auto-sklearn")

# 앙상블 클래스들 (pickle 가능하도록 모듈 레벨에 정의)
class VotingEnsemble:
    def __init__(self, voting_regressors):
        self.voting_regressors = voting_regressors
        self.is_fitted = False

    def fit(self, X, y):
        for i, voting_reg in enumerate(self.voting_regressors):
            voting_reg.fit(X, y[:, i])
        self.is_fitted = True
        return self

    def predict(self, X):
        if not self.is_fitted:
            raise ValueError("Model must be fitted before prediction")
        predictions = []
        for voting_reg in self.voting_regressors:
            pred = voting_reg.predict(X)
            predictions.append(pred)
        return np.column_stack(predictions)

class StackingEnsemble:
    def __init__(self, base_models, meta_model):
        self.base_models = base_models
        self.meta_model = meta_model
        self.is_fitted = True

    def predict(self, X):
        # 기본 모델들의 예측
        base_preds = []
        for name, model, score in self.base_models:
            pred = model.predict(X)
            base_preds.append(pred)

        # 메타 특성 생성
        meta_features = np.concatenate(base_preds, axis=1)

        # 메타 모델로 최종 예측
        return self.meta_model.predict(meta_features)

class BlendingEnsemble:
    def __init__(self, base_models, meta_model):
        self.base_models = base_models
        self.meta_model = meta_model
        self.is_fitted = True

    def predict(self, X):
        # 기본 모델들의 예측
        base_preds = []
        for name, model, score in self.base_models:
            pred = model.predict(X)
            base_preds.append(pred)

        # 메타 특성 생성
        meta_features = np.concatenate(base_preds, axis=1)

        # 메타 모델로 최종 예측
        return self.meta_model.predict(meta_features)

class GaitParameterCalibrator:
    """
    Velocity 기반 Gait Parameter 보정 클래스
    """
    
    def __init__(self, automl_time_budget=300):
        """
        Args:
            automl_time_budget (int): AutoML 학습 시간 제한 (초)
        """
        self.automl_time_budget = automl_time_budget
        self.models = {}
        self.scalers = {}
        self.target_columns = [
            'Stride_Len_L', 'Stride_Len_R',
            'Swing_Perc_L', 'Swing_Perc_R', 
            'Stance_Perc_L', 'Stance_Perc_R',
            'D_Supp_PercL', 'D_Supp_PercR'
        ]
        self.feature_column = 'Velocity'
        
    def load_data(self, excel_path='/datasets/gait/SingleView/gaitrite_full_dataset.xlsx'):
        """
        Excel 파일에서 데이터 로드 (StrideTm 변수들 완전 제외)
        """
        print("Loading data...")
        df = pd.read_excel(excel_path)

        # 필요한 컬럼만 선택 (StrideTm 변수들 제외)
        required_columns = [self.feature_column] + self.target_columns

        # StrideTm 관련 컬럼들이 있다면 제외
        available_columns = df.columns.tolist()
        filtered_columns = []

        for col in required_columns:
            if col in available_columns:
                filtered_columns.append(col)
            else:
                print(f"Warning: Column '{col}' not found in data")

        # StrideTm 컬럼들을 명시적으로 제외
        excluded_stride_time_cols = ['StrideTm_L', 'StrideTm_R']
        for col in excluded_stride_time_cols:
            if col in filtered_columns:
                filtered_columns.remove(col)
                print(f"Excluded StrideTm column: {col}")

        self.df = df[filtered_columns].copy()

        # 결측값 제거
        initial_count = len(self.df)
        self.df = self.df.dropna()
        after_na_count = len(self.df)

        print(f"Data loaded: {initial_count} samples")
        print(f"After removing NaN: {after_na_count} samples ({initial_count - after_na_count} removed)")
        print(f"Final columns: {list(self.df.columns)}")
        print(f"Target parameters: {self.target_columns}")

        return self.df

    def save_preprocessed_data(self, save_path='./data/preprocessed_gait_data.csv'):
        """
        전처리된 데이터를 CSV 파일로 저장

        Args:
            save_path (str): 저장할 파일 경로
        """
        import os

        # 디렉토리 생성
        os.makedirs(os.path.dirname(save_path), exist_ok=True)

        # 데이터 저장
        self.df.to_csv(save_path, index=False)

        print(f"\n=== Preprocessed Data Saved ===")
        print(f"File path: {save_path}")
        print(f"Data shape: {self.df.shape}")
        print(f"Columns: {list(self.df.columns)}")

        # 메타데이터 저장
        metadata = {
            'original_samples': getattr(self, 'original_sample_count', 'Unknown'),
            'final_samples': len(self.df),
            'removed_samples': getattr(self, 'original_sample_count', 0) - len(self.df) if hasattr(self, 'original_sample_count') else 'Unknown',
            'outlier_removal_method': getattr(self, 'outlier_method_used', 'Unknown'),
            'preprocessing_timestamp': pd.Timestamp.now().isoformat(),
            'columns': list(self.df.columns),
            'feature_column': self.feature_column,
            'target_columns': self.target_columns
        }

        metadata_path = save_path.replace('.csv', '_metadata.json')
        import json
        with open(metadata_path, 'w') as f:
            json.dump(metadata, f, indent=2)

        print(f"Metadata saved: {metadata_path}")

        return save_path

    def load_preprocessed_data(self, data_path='./data/preprocessed_gait_data.csv'):
        """
        저장된 전처리 데이터를 로드

        Args:
            data_path (str): 로드할 파일 경로

        Returns:
            pd.DataFrame: 로드된 데이터
        """
        import os

        if not os.path.exists(data_path):
            raise FileNotFoundError(f"Preprocessed data not found at {data_path}")

        print(f"\n=== Loading Preprocessed Data ===")
        print(f"File path: {data_path}")

        # 데이터 로드
        self.df = pd.read_csv(data_path)

        # 메타데이터 로드 (있는 경우)
        metadata_path = data_path.replace('.csv', '_metadata.json')
        if os.path.exists(metadata_path):
            import json
            with open(metadata_path, 'r') as f:
                metadata = json.load(f)

            print(f"Loaded preprocessed data:")
            print(f"  Original samples: {metadata.get('original_samples', 'Unknown')}")
            print(f"  Final samples: {metadata.get('final_samples', len(self.df))}")
            print(f"  Removed samples: {metadata.get('removed_samples', 'Unknown')}")
            print(f"  Outlier method: {metadata.get('outlier_removal_method', 'Unknown')}")
            print(f"  Preprocessing date: {metadata.get('preprocessing_timestamp', 'Unknown')}")
        else:
            print(f"Loaded data shape: {self.df.shape}")

        print(f"Columns: {list(self.df.columns)}")

        return self.df

    def remove_outliers(self, method='iqr', iqr_factor=1.5, z_threshold=3,
                       isolation_contamination=0.1, visualize=True):
        """
        아웃라이어 제거

        Args:
            method (str): 아웃라이어 탐지 방법 ('iqr', 'z_score', 'isolation_forest', 'combined')
            iqr_factor (float): IQR 방법의 배수 (기본: 1.5)
            z_threshold (float): Z-score 임계값 (기본: 3)
            isolation_contamination (float): Isolation Forest의 contamination 비율 (기본: 0.1)
            visualize (bool): 시각화 여부
        """
        print(f"\n=== Outlier Removal ({method.upper()}) ===")

        initial_count = len(self.df)
        self.original_sample_count = initial_count  # 메타데이터용
        self.outlier_method_used = method  # 메타데이터용
        outlier_indices = set()

        if method == 'iqr' or method == 'combined':
            # IQR 방법
            print("Applying IQR method...")
            for column in [self.feature_column] + self.target_columns:
                Q1 = self.df[column].quantile(0.25)
                Q3 = self.df[column].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - iqr_factor * IQR
                upper_bound = Q3 + iqr_factor * IQR

                column_outliers = self.df[(self.df[column] < lower_bound) |
                                        (self.df[column] > upper_bound)].index
                outlier_indices.update(column_outliers)

                print(f"  {column}: {len(column_outliers)} outliers (range: {lower_bound:.2f} - {upper_bound:.2f})")

        if method == 'z_score' or method == 'combined':
            # Z-score 방법
            print("Applying Z-score method...")
            from scipy import stats
            for column in [self.feature_column] + self.target_columns:
                z_scores = np.abs(stats.zscore(self.df[column]))
                column_outliers = self.df[z_scores > z_threshold].index
                outlier_indices.update(column_outliers)

                print(f"  {column}: {len(column_outliers)} outliers (|z| > {z_threshold})")

        if method == 'isolation_forest' or method == 'combined':
            # Isolation Forest 방법
            print("Applying Isolation Forest method...")
            from sklearn.ensemble import IsolationForest

            # 모든 수치형 컬럼에 대해 적용
            numeric_data = self.df[[self.feature_column] + self.target_columns]

            # 스케일링
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            scaled_data = scaler.fit_transform(numeric_data)

            # Isolation Forest 적용
            iso_forest = IsolationForest(contamination=isolation_contamination, random_state=42)
            outlier_labels = iso_forest.fit_predict(scaled_data)

            # -1이 아웃라이어
            iso_outliers = self.df[outlier_labels == -1].index
            outlier_indices.update(iso_outliers)

            print(f"  Isolation Forest: {len(iso_outliers)} outliers")

        # 아웃라이어 제거
        self.outlier_indices = list(outlier_indices)
        self.df_before_outlier_removal = self.df.copy()  # 백업
        self.df = self.df.drop(outlier_indices)

        final_count = len(self.df)
        removed_count = initial_count - final_count
        removal_percentage = (removed_count / initial_count) * 100

        print(f"\nOutlier removal summary:")
        print(f"  Initial samples: {initial_count}")
        print(f"  Outliers detected: {len(outlier_indices)}")
        print(f"  Samples removed: {removed_count}")
        print(f"  Final samples: {final_count}")
        print(f"  Removal percentage: {removal_percentage:.2f}%")

        # 시각화
        if visualize:
            self._visualize_outlier_removal()

        return self.df

    def _visualize_outlier_removal(self):
        """
        아웃라이어 제거 전후 비교 시각화
        """
        print("\n=== Outlier Removal Visualization ===")

        plt.figure(figsize=(20, 16))

        # 각 컬럼별 박스플롯 비교
        for i, column in enumerate([self.feature_column] + self.target_columns):
            # Before removal
            plt.subplot(3, 4, i+1)

            # 박스플롯
            box_data = [self.df_before_outlier_removal[column], self.df[column]]
            box_labels = ['Before', 'After']

            bp = plt.boxplot(box_data, labels=box_labels, patch_artist=True)
            bp['boxes'][0].set_facecolor('lightcoral')
            bp['boxes'][1].set_facecolor('lightblue')

            plt.title(f'{column}\nBefore: {len(self.df_before_outlier_removal)}, After: {len(self.df)}')
            plt.ylabel('Value')
            plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('outlier_removal_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 히스토그램 비교
        plt.figure(figsize=(20, 16))

        for i, column in enumerate([self.feature_column] + self.target_columns):
            plt.subplot(3, 4, i+1)

            # 히스토그램
            plt.hist(self.df_before_outlier_removal[column], bins=50, alpha=0.7,
                    label='Before', color='lightcoral', density=True)
            plt.hist(self.df[column], bins=50, alpha=0.7,
                    label='After', color='lightblue', density=True)

            plt.title(f'{column} Distribution')
            plt.xlabel('Value')
            plt.ylabel('Density')
            plt.legend()
            plt.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig('outlier_removal_histograms.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 통계 요약
        print("\n=== Statistical Summary ===")
        print("Before outlier removal:")
        print(self.df_before_outlier_removal.describe())
        print("\nAfter outlier removal:")
        print(self.df.describe())

    def explore_data(self):
        """
        데이터 탐색 및 시각화
        """
        print("\n=== Data Exploration ===")
        print(self.df.describe())
        
        # Velocity와 각 target parameter 간의 상관관계 분석
        correlations = {}
        for target in self.target_columns:
            corr = self.df[self.feature_column].corr(self.df[target])
            correlations[target] = corr
            
        # 상관관계 시각화
        plt.figure(figsize=(12, 8))
        corr_df = pd.DataFrame(list(correlations.items()), columns=['Parameter', 'Correlation'])
        sns.barplot(data=corr_df, x='Correlation', y='Parameter')
        plt.title('Correlation between Velocity and Gait Parameters')
        plt.tight_layout()
        plt.savefig('velocity_correlation.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 산점도 매트릭스
        plt.figure(figsize=(15, 12))
        for i, target in enumerate(self.target_columns):
            plt.subplot(3, 4, i+1)
            plt.scatter(self.df[self.feature_column], self.df[target], alpha=0.5)
            plt.xlabel('Velocity')
            plt.ylabel(target)
            plt.title(f'Velocity vs {target}\nCorr: {correlations[target]:.3f}')
        
        plt.tight_layout()
        plt.savefig('velocity_scatterplots.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        return correlations
    
    def prepare_data(self, test_size=0.2, random_state=42):
        """
        학습/테스트 데이터 분할
        """
        print("\n=== Data Preparation ===")
        
        X = self.df[[self.feature_column]]
        
        # 각 target parameter별로 데이터 분할
        self.train_test_splits = {}
        
        for target in self.target_columns:
            y = self.df[target]
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=test_size, random_state=random_state
            )
            
            # 스케일링 (선택사항 - AutoML이 자동으로 처리하지만 명시적으로 수행)
            scaler_X = StandardScaler()
            X_train_scaled = scaler_X.fit_transform(X_train)
            X_test_scaled = scaler_X.transform(X_test)
            
            self.train_test_splits[target] = {
                'X_train': X_train_scaled,
                'X_test': X_test_scaled,
                'y_train': y_train,
                'y_test': y_test,
                'scaler_X': scaler_X
            }
            
        print(f"Data split completed. Training size: {len(X_train)}, Test size: {len(X_test)}")
    
    def train_automl_models(self):
        """
        Velocity로부터 모든 target parameters를 동시에 예측하는 단일 멀티 아웃풋 모델 학습
        """
        print("\n=== Single Multi-Output AutoML Model Training ===")

        # 모든 target parameters를 하나의 y로 결합
        X_train = self.train_test_splits[self.target_columns[0]]['X_train']
        X_test = self.train_test_splits[self.target_columns[0]]['X_test']

        # 모든 target parameters의 y값들을 결합
        y_train_multi = np.column_stack([
            self.train_test_splits[target]['y_train'].values
            for target in self.target_columns
        ])
        y_test_multi = np.column_stack([
            self.train_test_splits[target]['y_test'].values
            for target in self.target_columns
        ])

        # 단일 멀티 아웃풋 모델 학습
        print(f"Training single multi-output model for all {len(self.target_columns)} parameters...")
        print(f"Input shape: {X_train.shape}, Output shape: {y_train_multi.shape}")

        if AUTOML_LIBRARY == 'flaml':
            # FLAML은 기본적으로 멀티 아웃풋을 지원하지 않으므로 sklearn의 MultiOutputRegressor 사용
            from sklearn.multioutput import MultiOutputRegressor
            from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
            from sklearn.linear_model import LinearRegression
            from sklearn.model_selection import cross_val_score

            print("FLAML doesn't support native multi-output. Using sklearn MultiOutputRegressor...")

            # 여러 기본 모델 테스트
            base_models = {
                'RandomForest': RandomForestRegressor(n_estimators=100, random_state=42),
                'GradientBoosting': GradientBoostingRegressor(n_estimators=100, random_state=42),
                'LinearRegression': LinearRegression()
            }

            best_model = None
            best_score = -np.inf
            best_name = None

            print("Testing different base models for multi-output...")
            for name, base_model in base_models.items():
                print(f"  Testing {name}...")
                multi_model = MultiOutputRegressor(base_model)

                # Cross-validation으로 성능 평가
                try:
                    scores = cross_val_score(multi_model, X_train, y_train_multi,
                                           cv=3, scoring='r2', n_jobs=-1)
                    avg_score = np.mean(scores)
                    print(f"    Average R² score: {avg_score:.4f}")

                    if avg_score > best_score:
                        best_score = avg_score
                        best_model = multi_model
                        best_name = name
                except Exception as e:
                    print(f"    Error with {name}: {e}")
                    continue

            if best_model is not None:
                print(f"Best model: {best_name} (R² = {best_score:.4f})")
                print("Training final model...")
                best_model.fit(X_train, y_train_multi)
                self.models['multi_output'] = best_model
            else:
                # 폴백: RandomForest 사용
                print("Using RandomForest as fallback...")
                fallback_model = MultiOutputRegressor(RandomForestRegressor(n_estimators=100, random_state=42))
                fallback_model.fit(X_train, y_train_multi)
                self.models['multi_output'] = fallback_model

        else:
            # Auto-sklearn 사용 (멀티 아웃풋 지원)
            from sklearn.multioutput import MultiOutputRegressor
            import autosklearn.regression

            print("Using Auto-sklearn with MultiOutputRegressor...")
            base_automl = autosklearn.regression.AutoSklearnRegressor(
                time_left_for_this_task=self.automl_time_budget,
                per_run_time_limit=30,
                memory_limit=3072
            )

            multi_automl = MultiOutputRegressor(base_automl)
            multi_automl.fit(X_train, y_train_multi)

            self.models['multi_output'] = multi_automl

        # 테스트 데이터 저장 (평가용)
        self.X_test = X_test
        self.y_test_multi = y_test_multi
        self.X_train = X_train
        self.y_train_multi = y_train_multi

        print("Single multi-output model training completed!")

    def train_ensemble_models(self, n_models=5, ensemble_method='voting'):
        """
        여러 AutoML 모델을 앙상블하여 성능 극대화

        Args:
            n_models (int): 앙상블할 모델 개수
            ensemble_method (str): 앙상블 방법 ('voting', 'stacking', 'blending')
        """
        print(f"\n=== Ensemble Multi-Output Model Training ({n_models} models) ===")

        # 데이터 준비
        X_train = self.train_test_splits[self.target_columns[0]]['X_train']
        X_test = self.train_test_splits[self.target_columns[0]]['X_test']

        y_train_multi = np.column_stack([
            self.train_test_splits[target]['y_train'].values
            for target in self.target_columns
        ])
        y_test_multi = np.column_stack([
            self.train_test_splits[target]['y_test'].values
            for target in self.target_columns
        ])

        print(f"Training {n_models} diverse models for ensemble...")
        print(f"Input shape: {X_train.shape}, Output shape: {y_train_multi.shape}")

        # 다양한 기본 모델들 정의
        from sklearn.multioutput import MultiOutputRegressor
        from sklearn.ensemble import (RandomForestRegressor, GradientBoostingRegressor,
                                    ExtraTreesRegressor, AdaBoostRegressor, BaggingRegressor)
        from sklearn.linear_model import Ridge, ElasticNet
        from sklearn.svm import SVR
        from sklearn.neural_network import MLPRegressor
        import xgboost as xgb
        from sklearn.tree import DecisionTreeRegressor

        # 앙상블용 다양한 모델 풀
        model_pool = [
            ('RandomForest_100', MultiOutputRegressor(
                RandomForestRegressor(n_estimators=100, max_depth=15, random_state=42, n_jobs=-1))),
            ('RandomForest_200', MultiOutputRegressor(
                RandomForestRegressor(n_estimators=200, max_depth=20, random_state=43, n_jobs=-1))),
            ('GradientBoosting_100', MultiOutputRegressor(
                GradientBoostingRegressor(n_estimators=100, learning_rate=0.1, max_depth=5, random_state=42))),
            ('GradientBoosting_200', MultiOutputRegressor(
                GradientBoostingRegressor(n_estimators=200, learning_rate=0.05, max_depth=7, random_state=43))),
            ('ExtraTrees', MultiOutputRegressor(
                ExtraTreesRegressor(n_estimators=150, max_depth=20, random_state=42, n_jobs=-1))),
            ('XGBoost_100', MultiOutputRegressor(
                xgb.XGBRegressor(n_estimators=100, learning_rate=0.1, max_depth=5, random_state=42, n_jobs=-1))),
            ('XGBoost_200', MultiOutputRegressor(
                xgb.XGBRegressor(n_estimators=200, learning_rate=0.05, max_depth=7, random_state=43, n_jobs=-1))),
            ('Ridge', MultiOutputRegressor(Ridge(alpha=1.0, random_state=42))),
            ('ElasticNet', MultiOutputRegressor(ElasticNet(alpha=0.1, l1_ratio=0.5, random_state=42, max_iter=2000))),
            ('MLP', MultiOutputRegressor(
                MLPRegressor(hidden_layer_sizes=(100, 50), learning_rate_init=0.01, random_state=42, max_iter=1000))),
            ('AdaBoost', MultiOutputRegressor(
                AdaBoostRegressor(n_estimators=100, learning_rate=0.1, random_state=42))),
            ('Bagging', MultiOutputRegressor(
                BaggingRegressor(n_estimators=100, random_state=42, n_jobs=-1)))
        ]

        # 모델 성능 평가 및 선택
        model_scores = []
        trained_models = []

        print("Evaluating models for ensemble selection...")
        for name, model in model_pool:
            try:
                print(f"  Training {name}...")
                model.fit(X_train, y_train_multi)

                # 검증 성능 계산
                y_pred = model.predict(X_test)
                avg_r2 = np.mean([r2_score(y_test_multi[:, i], y_pred[:, i])
                                for i in range(len(self.target_columns))])

                model_scores.append((name, model, avg_r2))
                trained_models.append((name, model))
                print(f"    Average R² score: {avg_r2:.4f}")

            except Exception as e:
                print(f"    Error training {name}: {e}")
                continue

        # 성능 순으로 정렬하고 상위 n_models 선택
        model_scores.sort(key=lambda x: x[2], reverse=True)
        selected_models = model_scores[:n_models]

        print(f"\nSelected top {n_models} models for ensemble:")
        for i, (name, model, score) in enumerate(selected_models):
            print(f"  {i+1}. {name}: R² = {score:.4f}")

        # 앙상블 모델 생성
        if ensemble_method == 'voting':
            ensemble_model = self._create_voting_ensemble(selected_models, X_train, y_train_multi)
        elif ensemble_method == 'stacking':
            ensemble_model = self._create_stacking_ensemble(selected_models, X_train, y_train_multi, X_test, y_test_multi)
        elif ensemble_method == 'blending':
            ensemble_model = self._create_blending_ensemble(selected_models, X_train, y_train_multi, X_test, y_test_multi)
        else:
            raise ValueError(f"Unknown ensemble method: {ensemble_method}")

        # 앙상블 모델 저장
        self.models['ensemble'] = ensemble_model
        self.models['ensemble_components'] = selected_models
        self.models['ensemble_method'] = ensemble_method

        # 테스트 데이터 저장
        self.X_test = X_test
        self.y_test_multi = y_test_multi
        self.X_train = X_train
        self.y_train_multi = y_train_multi

        print(f"\nEnsemble model ({ensemble_method}) training completed!")

    def _create_voting_ensemble(self, selected_models, X_train, y_train_multi):
        """
        Voting 앙상블 생성 (평균 기반)
        """
        from sklearn.ensemble import VotingRegressor
        from sklearn.multioutput import MultiOutputRegressor

        print("Creating voting ensemble...")

        # 각 출력별로 VotingRegressor 생성
        voting_regressors = []
        for i, target in enumerate(self.target_columns):
            # 각 모델의 i번째 출력을 위한 단일 출력 모델들 추출
            single_output_models = []
            for name, multi_model, score in selected_models:
                # MultiOutputRegressor에서 i번째 estimator 추출
                single_estimator = multi_model.estimators_[i]
                single_output_models.append((f"{name}_{i}", single_estimator))

            # VotingRegressor 생성
            voting_reg = VotingRegressor(single_output_models)
            voting_regressors.append(voting_reg)

        # 앙상블 생성 및 학습
        ensemble = VotingEnsemble(voting_regressors)
        ensemble.fit(X_train, y_train_multi)
        return ensemble

    def _create_stacking_ensemble(self, selected_models, X_train, y_train_multi, X_test, y_test_multi):
        """
        Stacking 앙상블 생성 (메타 모델 기반)
        """
        from sklearn.model_selection import cross_val_predict
        from sklearn.linear_model import Ridge
        from sklearn.multioutput import MultiOutputRegressor

        print("Creating stacking ensemble...")

        # 1단계: 기본 모델들의 교차 검증 예측값 생성
        base_predictions_train = []
        base_predictions_test = []

        for name, model, score in selected_models:
            print(f"  Generating predictions for {name}...")

            # 교차 검증으로 훈련 데이터 예측
            cv_pred = cross_val_predict(model, X_train, y_train_multi, cv=3, n_jobs=-1)
            base_predictions_train.append(cv_pred)

            # 테스트 데이터 예측
            test_pred = model.predict(X_test)
            base_predictions_test.append(test_pred)

        # 2단계: 메타 특성 생성
        meta_features_train = np.concatenate(base_predictions_train, axis=1)
        meta_features_test = np.concatenate(base_predictions_test, axis=1)

        print(f"  Meta features shape: {meta_features_train.shape}")

        # 3단계: 메타 모델 학습
        meta_model = MultiOutputRegressor(Ridge(alpha=1.0))
        meta_model.fit(meta_features_train, y_train_multi)

        # 스택킹 앙상블 생성
        ensemble = StackingEnsemble(selected_models, meta_model)
        return ensemble

    def _create_blending_ensemble(self, selected_models, X_train, y_train_multi, X_test, y_test_multi):
        """
        Blending 앙상블 생성 (홀드아웃 기반)
        """
        from sklearn.model_selection import train_test_split
        from sklearn.linear_model import Ridge
        from sklearn.multioutput import MultiOutputRegressor

        print("Creating blending ensemble...")

        # 1단계: 훈련 데이터를 블렌딩용으로 분할
        X_blend_train, X_blend_holdout, y_blend_train, y_blend_holdout = train_test_split(
            X_train, y_train_multi, test_size=0.2, random_state=42
        )

        # 2단계: 기본 모델들을 블렌딩 훈련 데이터로 재학습
        blend_predictions_holdout = []
        blend_predictions_test = []
        retrained_models = []

        for name, model, score in selected_models:
            print(f"  Retraining {name} for blending...")

            # 새로운 모델 인스턴스 생성 (같은 설정)
            from copy import deepcopy
            new_model = deepcopy(model)

            # 블렌딩 훈련 데이터로 학습
            new_model.fit(X_blend_train, y_blend_train)

            # 홀드아웃 데이터 예측
            holdout_pred = new_model.predict(X_blend_holdout)
            blend_predictions_holdout.append(holdout_pred)

            # 테스트 데이터 예측
            test_pred = new_model.predict(X_test)
            blend_predictions_test.append(test_pred)

            retrained_models.append((name, new_model, score))

        # 3단계: 메타 특성 생성
        meta_features_holdout = np.concatenate(blend_predictions_holdout, axis=1)
        meta_features_test = np.concatenate(blend_predictions_test, axis=1)

        print(f"  Meta features shape: {meta_features_holdout.shape}")

        # 4단계: 메타 모델 학습
        meta_model = MultiOutputRegressor(Ridge(alpha=1.0))
        meta_model.fit(meta_features_holdout, y_blend_holdout)

        # 블렌딩 앙상블 생성
        ensemble = BlendingEnsemble(retrained_models, meta_model)
        return ensemble

    def evaluate_ensemble_models(self):
        """
        앙상블 모델과 개별 모델들의 성능 비교 평가
        """
        print("\n=== Ensemble Model Evaluation ===")

        results = []

        # 앙상블 모델 예측
        if 'ensemble' in self.models:
            print("Evaluating ensemble model...")
            y_pred_train_ensemble = self.models['ensemble'].predict(self.X_train)
            y_pred_test_ensemble = self.models['ensemble'].predict(self.X_test)

            # 앙상블 성능 계산
            for i, target in enumerate(self.target_columns):
                y_true_train = self.y_train_multi[:, i]
                y_true_test = self.y_test_multi[:, i]
                y_pred_train = y_pred_train_ensemble[:, i]
                y_pred_test = y_pred_test_ensemble[:, i]

                train_r2 = r2_score(y_true_train, y_pred_train)
                test_r2 = r2_score(y_true_test, y_pred_test)
                train_mse = mean_squared_error(y_true_train, y_pred_train)
                test_mse = mean_squared_error(y_true_test, y_pred_test)
                train_mae = mean_absolute_error(y_true_train, y_pred_train)
                test_mae = mean_absolute_error(y_true_test, y_pred_test)

                result = {
                    'Model': f'Ensemble_{self.models.get("ensemble_method", "unknown")}',
                    'Parameter': target,
                    'Train_R2': train_r2,
                    'Test_R2': test_r2,
                    'Train_MSE': train_mse,
                    'Test_MSE': test_mse,
                    'Train_MAE': train_mae,
                    'Test_MAE': test_mae
                }
                results.append(result)

                print(f"Ensemble - {target}:")
                print(f"  Train R²: {train_r2:.4f}, Test R²: {test_r2:.4f}")

        # 개별 모델들 성능 계산 (비교용)
        if 'ensemble_components' in self.models:
            print("\nEvaluating individual component models...")
            for name, model, original_score in self.models['ensemble_components']:
                print(f"Evaluating {name}...")

                y_pred_train_individual = model.predict(self.X_train)
                y_pred_test_individual = model.predict(self.X_test)

                for i, target in enumerate(self.target_columns):
                    y_true_train = self.y_train_multi[:, i]
                    y_true_test = self.y_test_multi[:, i]
                    y_pred_train = y_pred_train_individual[:, i]
                    y_pred_test = y_pred_test_individual[:, i]

                    train_r2 = r2_score(y_true_train, y_pred_train)
                    test_r2 = r2_score(y_true_test, y_pred_test)
                    train_mse = mean_squared_error(y_true_train, y_pred_train)
                    test_mse = mean_squared_error(y_true_test, y_pred_test)
                    train_mae = mean_absolute_error(y_true_train, y_pred_train)
                    test_mae = mean_absolute_error(y_true_test, y_pred_test)

                    result = {
                        'Model': name,
                        'Parameter': target,
                        'Train_R2': train_r2,
                        'Test_R2': test_r2,
                        'Train_MSE': train_mse,
                        'Test_MSE': test_mse,
                        'Train_MAE': train_mae,
                        'Test_MAE': test_mae
                    }
                    results.append(result)

        # 결과 DataFrame 생성
        results_df = pd.DataFrame(results)

        # 성능 비교 요약
        if len(results) > 0:
            print("\n=== Performance Comparison ===")

            # 모델별 평균 성능
            model_performance = results_df.groupby('Model')['Test_R2'].agg(['mean', 'std']).round(4)
            model_performance = model_performance.sort_values('mean', ascending=False)

            print("Average Test R² by Model:")
            for model_name, (mean_r2, std_r2) in model_performance.iterrows():
                print(f"  {model_name:25s}: {mean_r2:.4f} ± {std_r2:.4f}")

            # 최고 성능 모델
            best_model = model_performance.index[0]
            best_score = model_performance.loc[best_model, 'mean']
            print(f"\n🏆 Best Model: {best_model} (R² = {best_score:.4f})")

            # 앙상블 개선 효과
            if 'ensemble' in [r['Model'] for r in results]:
                ensemble_scores = results_df[results_df['Model'].str.contains('Ensemble')]['Test_R2']
                individual_scores = results_df[~results_df['Model'].str.contains('Ensemble')]['Test_R2']

                if len(individual_scores) > 0:
                    ensemble_avg = ensemble_scores.mean()
                    individual_avg = individual_scores.mean()
                    improvement = ensemble_avg - individual_avg

                    print(f"\n📈 Ensemble Improvement:")
                    print(f"  Ensemble Average R²: {ensemble_avg:.4f}")
                    print(f"  Individual Average R²: {individual_avg:.4f}")
                    print(f"  Improvement: +{improvement:.4f} ({improvement/individual_avg*100:.1f}%)")

        self.ensemble_results_df = results_df
        self.y_pred_train_ensemble = y_pred_train_ensemble if 'ensemble' in self.models else None
        self.y_pred_test_ensemble = y_pred_test_ensemble if 'ensemble' in self.models else None

        return results_df
    
    def evaluate_models(self):
        """
        단일 멀티 아웃풋 모델의 성능 평가
        """
        print("\n=== Single Multi-Output Model Evaluation ===")

        results = []

        # 단일 멀티 아웃풋 모델로 예측 수행
        print("Performing predictions with single multi-output model...")
        y_pred_train_multi = self.models['multi_output'].predict(self.X_train)
        y_pred_test_multi = self.models['multi_output'].predict(self.X_test)

        # 각 target parameter별로 성능 평가
        for i, target in enumerate(self.target_columns):
            y_true_train = self.y_train_multi[:, i]
            y_true_test = self.y_test_multi[:, i]
            y_pred_train = y_pred_train_multi[:, i]
            y_pred_test = y_pred_test_multi[:, i]

            # 성능 지표 계산
            train_r2 = r2_score(y_true_train, y_pred_train)
            test_r2 = r2_score(y_true_test, y_pred_test)
            train_mse = mean_squared_error(y_true_train, y_pred_train)
            test_mse = mean_squared_error(y_true_test, y_pred_test)
            train_mae = mean_absolute_error(y_true_train, y_pred_train)
            test_mae = mean_absolute_error(y_true_test, y_pred_test)

            result = {
                'Parameter': target,
                'Train_R2': train_r2,
                'Test_R2': test_r2,
                'Train_MSE': train_mse,
                'Test_MSE': test_mse,
                'Train_MAE': train_mae,
                'Test_MAE': test_mae
            }
            results.append(result)

            print(f"{target}:")
            print(f"  Train R²: {train_r2:.4f}, Test R²: {test_r2:.4f}")
            print(f"  Train MSE: {train_mse:.4f}, Test MSE: {test_mse:.4f}")
            print(f"  Train MAE: {train_mae:.4f}, Test MAE: {test_mae:.4f}")

        # 전체 평균 성능
        avg_train_r2 = np.mean([r['Train_R2'] for r in results])
        avg_test_r2 = np.mean([r['Test_R2'] for r in results])
        print(f"\nOverall Performance (Single Multi-Output Model):")
        print(f"  Average Train R²: {avg_train_r2:.4f}")
        print(f"  Average Test R²: {avg_test_r2:.4f}")

        self.results_df = pd.DataFrame(results)
        self.y_pred_train_multi = y_pred_train_multi
        self.y_pred_test_multi = y_pred_test_multi

        return self.results_df
    
    def visualize_predictions(self):
        """
        멀티 아웃풋 모델의 예측 결과 시각화
        """
        print("\n=== Multi-Output Prediction Visualization ===")

        plt.figure(figsize=(15, 12))

        for i, target in enumerate(self.target_columns):
            y_true_test = self.y_test_multi[:, i]
            y_pred_test = self.y_pred_test_multi[:, i]

            plt.subplot(3, 4, i+1)
            plt.scatter(y_true_test, y_pred_test, alpha=0.6)

            # 완벽한 예측선 (y=x)
            min_val = min(y_true_test.min(), y_pred_test.min())
            max_val = max(y_true_test.max(), y_pred_test.max())
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8)

            plt.xlabel('Actual')
            plt.ylabel('Predicted')
            plt.title(f'{target}\nR² = {r2_score(y_true_test, y_pred_test):.3f}')

        plt.tight_layout()
        plt.savefig('prediction_results.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    def predict_calibrated_parameters(self, velocity_values, use_ensemble=True):
        """
        주어진 Velocity 값들에 대해 모델로 모든 gait parameters 동시 예측

        Args:
            velocity_values (array-like): Velocity 값들
            use_ensemble (bool): 앙상블 모델 사용 여부 (False면 단일 모델 사용)

        Returns:
            dict: 각 parameter별 예측값들
        """
        velocity_values = np.array(velocity_values).reshape(-1, 1)

        # 스케일링 적용 (첫 번째 target의 scaler 사용 - 모두 동일한 X 스케일러)
        scaler = self.train_test_splits[self.target_columns[0]]['scaler_X']
        velocity_scaled = scaler.transform(velocity_values)

        # 모델 선택 및 예측
        if use_ensemble and 'ensemble' in self.models:
            print("Using ensemble model for prediction...")
            predictions_array = self.models['ensemble'].predict(velocity_scaled)
        elif 'multi_output' in self.models:
            print("Using single multi-output model for prediction...")
            predictions_array = self.models['multi_output'].predict(velocity_scaled)
        else:
            raise ValueError("No trained model found. Please train a model first.")

        # 결과를 딕셔너리로 변환
        predictions = {}
        for i, target in enumerate(self.target_columns):
            predictions[target] = predictions_array[:, i]

        return predictions
    
    def save_models(self, save_dir='./models'):
        """
        학습된 모델 저장 (단일 모델 또는 앙상블 모델)
        """
        import os
        import pickle

        os.makedirs(save_dir, exist_ok=True)

        # 모델 저장 (우선순위: ensemble > multi_output)
        if 'ensemble' in self.models:
            # 앙상블 모델 저장
            model_path = os.path.join(save_dir, 'ensemble_model.pkl')
            with open(model_path, 'wb') as f:
                pickle.dump(self.models['ensemble'], f)

            # 앙상블 메타데이터 저장
            ensemble_info = {
                'method': self.models.get('ensemble_method', 'unknown'),
                'n_components': len(self.models.get('ensemble_components', [])),
                'component_names': [name for name, _, _ in self.models.get('ensemble_components', [])]
            }
            ensemble_info_path = os.path.join(save_dir, 'ensemble_info.pkl')
            with open(ensemble_info_path, 'wb') as f:
                pickle.dump(ensemble_info, f)

            print(f"Ensemble model saved to {save_dir}")
            print(f"  - Model: ensemble_model.pkl ({ensemble_info['method']} with {ensemble_info['n_components']} models)")
            print(f"  - Info: ensemble_info.pkl")

        elif 'multi_output' in self.models:
            # 단일 멀티 아웃풋 모델 저장
            model_path = os.path.join(save_dir, 'multi_output_model.pkl')
            with open(model_path, 'wb') as f:
                pickle.dump(self.models['multi_output'], f)

            print(f"Single model saved to {save_dir}")
            print(f"  - Model: multi_output_model.pkl")
        else:
            print("No trained model found to save.")
            return

        # 스케일러 저장 (모든 target이 같은 X 스케일러 사용)
        if hasattr(self, 'train_test_splits') and self.target_columns:
            scaler_path = os.path.join(save_dir, 'velocity_scaler.pkl')
            with open(scaler_path, 'wb') as f:
                pickle.dump(self.train_test_splits[self.target_columns[0]]['scaler_X'], f)
            print(f"  - Scaler: velocity_scaler.pkl")

        # Target columns 정보 저장
        target_info_path = os.path.join(save_dir, 'target_columns.pkl')
        with open(target_info_path, 'wb') as f:
            pickle.dump(self.target_columns, f)
        print(f"  - Target info: target_columns.pkl")


def main(outlier_method='combined', outlier_removal=True, save_preprocessed=True):
    """
    메인 실행 함수

    Args:
        outlier_method (str): 아웃라이어 제거 방법 ('iqr', 'z_score', 'isolation_forest', 'combined')
        outlier_removal (bool): 아웃라이어 제거 여부
        save_preprocessed (bool): 전처리된 데이터 저장 여부
    """
    print("=== Gait Parameter Calibration using AutoML ===")

    # 캘리브레이터 초기화
    calibrator = GaitParameterCalibrator(automl_time_budget=300)  # 5분

    # 데이터 로드
    df = calibrator.load_data()

    # 아웃라이어 제거
    if outlier_removal:
        df = calibrator.remove_outliers(method=outlier_method, visualize=True)

        # 전처리된 데이터 저장
        if save_preprocessed:
            save_path = f'./data/preprocessed_gait_data_{outlier_method}.csv'
            calibrator.save_preprocessed_data(save_path)
    else:
        print("\nSkipping outlier removal...")
        if save_preprocessed:
            # 아웃라이어 제거 없이도 저장 (원본 데이터)
            calibrator.original_sample_count = len(df)
            calibrator.outlier_method_used = 'none'
            save_path = './data/preprocessed_gait_data_original.csv'
            calibrator.save_preprocessed_data(save_path)

    # 데이터 탐색
    correlations = calibrator.explore_data()

    # 데이터 준비
    calibrator.prepare_data()

    # AutoML 모델 학습
    calibrator.train_automl_models()

    # 모델 평가
    results_df = calibrator.evaluate_models()

    # 예측 결과 시각화
    calibrator.visualize_predictions()

    # 결과 요약
    print("\n=== Summary ===")
    print("Best performing parameters (by Test R²):")
    best_params = results_df.nlargest(5, 'Test_R2')[['Parameter', 'Test_R2', 'Test_MSE']]
    print(best_params)

    # 모델 저장
    calibrator.save_models()

    # 예시: 특정 velocity 값에 대한 예측 (cm/s 단위로 수정)
    print("\n=== Example Prediction ===")
    example_velocities = [80, 100, 120]  # cm/s
    predictions = calibrator.predict_calibrated_parameters(example_velocities)

    for i, vel in enumerate(example_velocities):
        print(f"\nVelocity: {vel} cm/s")
        for param, values in predictions.items():
            print(f"  {param}: {values[i]:.3f}")

    return calibrator


class PreprocessedGaitCalibrator(GaitParameterCalibrator):
    """
    전처리된 데이터를 기반으로 학습하는 캘리브레이터
    """

    def __init__(self, preprocessed_data_path='./data/preprocessed_gait_data_iqr.csv',
                 automl_time_budget=300):
        """
        Args:
            preprocessed_data_path (str): 전처리된 데이터 파일 경로
            automl_time_budget (int): AutoML 학습 시간 제한 (초)
        """
        super().__init__(automl_time_budget)
        self.preprocessed_data_path = preprocessed_data_path

    def load_data(self, excel_path=None):
        """
        전처리된 데이터를 로드 (excel_path는 무시됨)
        """
        return self.load_preprocessed_data(self.preprocessed_data_path)

    def train_from_preprocessed_data(self, data_path=None, visualize=True):
        """
        전처리된 데이터로부터 모델 학습

        Args:
            data_path (str): 전처리된 데이터 경로 (None이면 기본값 사용)
            visualize (bool): 시각화 여부

        Returns:
            pd.DataFrame: 평가 결과
        """
        print("=== Training from Preprocessed Data ===")

        # 데이터 로드
        if data_path:
            self.preprocessed_data_path = data_path

        self.load_data()

        # 데이터 탐색 (선택사항)
        if visualize:
            correlations = self.explore_data()

        # 데이터 준비
        self.prepare_data()

        # AutoML 모델 학습
        self.train_automl_models()

        # 모델 평가
        results_df = self.evaluate_models()

        # 예측 결과 시각화
        if visualize:
            self.visualize_predictions()

        # 모델 저장
        self.save_models()

        return results_df


def preprocess_and_save_data(outlier_method='iqr', visualize=True):
    """
    데이터 전처리 및 저장 전용 함수

    Args:
        outlier_method (str): 아웃라이어 제거 방법
        visualize (bool): 시각화 여부

    Returns:
        str: 저장된 파일 경로
    """
    print("=== Data Preprocessing and Saving ===")

    # 캘리브레이터 초기화
    calibrator = GaitParameterCalibrator()

    # 데이터 로드
    df = calibrator.load_data()

    # 아웃라이어 제거
    df = calibrator.remove_outliers(method=outlier_method, visualize=visualize)

    # 전처리된 데이터 저장
    save_path = f'./data/preprocessed_gait_data_{outlier_method}.csv'
    saved_path = calibrator.save_preprocessed_data(save_path)

    print(f"\n✅ Preprocessing completed!")
    print(f"📁 Saved to: {saved_path}")

    return saved_path


def train_from_saved_data(data_path='./data/preprocessed_gait_data_iqr.csv',
                         automl_time_budget=600):
    """
    저장된 전처리 데이터로부터 모델 학습

    Args:
        data_path (str): 전처리된 데이터 파일 경로
        automl_time_budget (int): AutoML 학습 시간

    Returns:
        PreprocessedGaitCalibrator: 학습된 캘리브레이터
    """
    print("=== Training from Saved Preprocessed Data ===")

    # 전처리된 데이터 기반 캘리브레이터 초기화
    calibrator = PreprocessedGaitCalibrator(
        preprocessed_data_path=data_path,
        automl_time_budget=automl_time_budget
    )

    # 학습 실행
    results_df = calibrator.train_from_preprocessed_data(visualize=True)

    # 결과 요약
    print("\n=== Training Results Summary ===")
    print("Best performing parameters (by Test R²):")
    best_params = results_df.nlargest(5, 'Test_R2')[['Parameter', 'Test_R2', 'Test_MSE']]
    print(best_params)

    return calibrator


if __name__ == "__main__":
    calibrator = main()

"""
딥러닝 모델과 Velocity 기반 보정 시스템 통합 예시
"""

import numpy as np
import pandas as pd
from calibration_utils import GaitCalibrationPredictor
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt

class MockDeepLearningModel:
    """
    실제 딥러닝 모델을 시뮬레이션하는 Mock 클래스
    (실제 사용 시에는 실제 딥러닝 모델로 교체)
    """
    
    def __init__(self, noise_level: float = 0.1):
        """
        Args:
            noise_level (float): 예측에 추가할 노이즈 레벨
        """
        self.noise_level = noise_level
        
    def predict_gait_parameters(self, input_data) -> Dict[str, float]:
        """
        Mock 딥러닝 모델의 gait parameter 예측
        실제로는 부정확한 velocity와 함께 다른 parameters도 예측
        
        Args:
            input_data: 입력 데이터 (실제로는 비디오 프레임, 센서 데이터 등)
            
        Returns:
            Dict[str, float]: 예측된 gait parameters
        """
        # 실제 값에 노이즈를 추가하여 부정확한 예측 시뮬레이션
        true_velocity = 80.0  # 실제 속도 (cm/s)
        
        # 부정확한 velocity 예측 (±20% 오차) - cm/s 단위
        predicted_velocity = true_velocity * (1 + np.random.uniform(-0.2, 0.2))
        
        # 다른 parameters도 velocity 오차의 영향을 받아 부정확하게 예측
        velocity_error_factor = predicted_velocity / true_velocity
        
        # 기본값들 (실제로는 더 복잡한 모델 출력) - cm/s 단위에 맞춤
        base_predictions = {
            'Velocity': predicted_velocity,
            'Stride_Len_L': 85.0 * velocity_error_factor + np.random.normal(0, self.noise_level * 10),
            'Stride_Len_R': 84.5 * velocity_error_factor + np.random.normal(0, self.noise_level * 10),
            'StrideTm_L': 1.15 / velocity_error_factor + np.random.normal(0, self.noise_level * 0.1),
            'StrideTm_R': 1.16 / velocity_error_factor + np.random.normal(0, self.noise_level * 0.1),
            'Swing_Perc_L': 35.0 + np.random.normal(0, self.noise_level * 2),
            'Swing_Perc_R': 35.2 + np.random.normal(0, self.noise_level * 2),
            'Stance_Perc_L': 65.0 + np.random.normal(0, self.noise_level * 2),
            'Stance_Perc_R': 64.8 + np.random.normal(0, self.noise_level * 2),
            'D_Supp_PercL': 30.0 + np.random.normal(0, self.noise_level * 3),
            'D_Supp_PercR': 30.2 + np.random.normal(0, self.noise_level * 3)
        }
        
        return base_predictions

class IntegratedGaitAnalysisSystem:
    """
    딥러닝 모델과 Velocity 기반 보정을 통합한 시스템
    """
    
    def __init__(self, 
                 deep_learning_model,
                 calibration_models_dir: str = './models',
                 default_calibration_weight: float = 0.6):
        """
        Args:
            deep_learning_model: 딥러닝 모델 인스턴스
            calibration_models_dir (str): 보정 모델들이 저장된 디렉토리
            default_calibration_weight (float): 기본 보정 가중치
        """
        self.dl_model = deep_learning_model
        self.calibration_weight = default_calibration_weight
        
        # 보정 시스템 초기화
        try:
            self.calibrator = GaitCalibrationPredictor(calibration_models_dir)
            self.calibration_available = True
            print("Calibration system loaded successfully")
        except (FileNotFoundError, ValueError) as e:
            print(f"Calibration system not available: {e}")
            self.calibration_available = False
    
    def analyze_gait(self, 
                    input_data, 
                    measured_velocity: float = None,
                    calibration_weight: float = None) -> Dict[str, float]:
        """
        통합 gait 분석 수행
        
        Args:
            input_data: 딥러닝 모델의 입력 데이터
            measured_velocity (float): 정확하게 측정된 velocity (있는 경우)
            calibration_weight (float): 보정 가중치 (None이면 기본값 사용)
            
        Returns:
            Dict[str, float]: 최종 분석 결과
        """
        # 1. 딥러닝 모델로 초기 예측
        dl_predictions = self.dl_model.predict_gait_parameters(input_data)
        
        # 2. 보정 시스템이 사용 가능하고 정확한 velocity가 주어진 경우 멀티 아웃풋 보정 수행
        if self.calibration_available and measured_velocity is not None:
            weight = calibration_weight if calibration_weight is not None else self.calibration_weight

            calibrated_predictions = self.calibrator.calibrate_deep_learning_output(
                dl_predictions=dl_predictions,
                measured_velocity=measured_velocity,
                calibration_weight=weight
            )
            
            return {
                'original_predictions': dl_predictions,
                'calibrated_predictions': calibrated_predictions,
                'final_predictions': calibrated_predictions,
                'calibration_applied': True,
                'measured_velocity': measured_velocity,
                'predicted_velocity': dl_predictions.get('Velocity', None)
            }
        else:
            return {
                'original_predictions': dl_predictions,
                'final_predictions': dl_predictions,
                'calibration_applied': False,
                'predicted_velocity': dl_predictions.get('Velocity', None)
            }
    
    def batch_analysis(self, 
                      input_data_list: List,
                      measured_velocities: List[float] = None) -> pd.DataFrame:
        """
        여러 데이터에 대한 배치 분석
        
        Args:
            input_data_list (List): 입력 데이터들의 리스트
            measured_velocities (List[float]): 측정된 velocity 값들 (선택사항)
            
        Returns:
            pd.DataFrame: 배치 분석 결과
        """
        results = []
        
        for i, input_data in enumerate(input_data_list):
            measured_vel = measured_velocities[i] if measured_velocities else None
            
            result = self.analyze_gait(input_data, measured_vel)
            
            # 결과 정리
            row = {'sample_id': i}
            
            # 원본 예측값들
            for key, value in result['original_predictions'].items():
                row[f'original_{key}'] = value
            
            # 보정된 예측값들 (있는 경우)
            if result['calibration_applied']:
                for key, value in result['calibrated_predictions'].items():
                    row[f'calibrated_{key}'] = value
                row['measured_velocity'] = measured_vel
            
            row['calibration_applied'] = result['calibration_applied']
            results.append(row)
        
        return pd.DataFrame(results)
    
    def evaluate_calibration_impact(self, 
                                  test_data_list: List,
                                  true_velocities: List[float],
                                  ground_truth_df: pd.DataFrame = None) -> Dict:
        """
        보정의 영향을 평가
        
        Args:
            test_data_list (List): 테스트 데이터들
            true_velocities (List[float]): 실제 velocity 값들
            ground_truth_df (pd.DataFrame): 실제값들 (선택사항)
            
        Returns:
            Dict: 평가 결과
        """
        # 보정 전후 결과 수집
        original_results = []
        calibrated_results = []
        
        for i, (input_data, true_vel) in enumerate(zip(test_data_list, true_velocities)):
            # 보정 없이 분석
            result_no_cal = self.analyze_gait(input_data, measured_velocity=None)
            original_results.append(result_no_cal['original_predictions'])
            
            # 보정 적용하여 분석
            result_with_cal = self.analyze_gait(input_data, measured_velocity=true_vel)
            if result_with_cal['calibration_applied']:
                calibrated_results.append(result_with_cal['calibrated_predictions'])
            else:
                calibrated_results.append(result_with_cal['original_predictions'])
        
        # 결과 분석
        original_df = pd.DataFrame(original_results)
        calibrated_df = pd.DataFrame(calibrated_results)
        
        # Velocity 오차 계산
        velocity_errors_original = np.abs(original_df['Velocity'] - true_velocities)
        velocity_errors_calibrated = np.abs(calibrated_df['Velocity'] - true_velocities)
        
        evaluation = {
            'original_velocity_mae': np.mean(velocity_errors_original),
            'calibrated_velocity_mae': np.mean(velocity_errors_calibrated),
            'velocity_improvement': np.mean(velocity_errors_original) - np.mean(velocity_errors_calibrated),
            'samples_tested': len(test_data_list),
            'calibration_available': self.calibration_available
        }
        
        # Ground truth가 있는 경우 추가 평가
        if ground_truth_df is not None:
            from calibration_utils import evaluate_calibration_performance
            performance_comparison = evaluate_calibration_performance(
                original_df, calibrated_df, ground_truth_df
            )
            evaluation['detailed_performance'] = performance_comparison
        
        return evaluation

def demo_integration():
    """
    통합 시스템 데모
    """
    print("=== Integrated Gait Analysis System Demo ===")
    
    # Mock 딥러닝 모델 초기화
    dl_model = MockDeepLearningModel(noise_level=0.15)
    
    # 통합 시스템 초기화
    integrated_system = IntegratedGaitAnalysisSystem(
        deep_learning_model=dl_model,
        default_calibration_weight=0.7
    )
    
    # 시뮬레이션 데이터 생성
    n_samples = 10
    input_data_list = [f"sample_{i}" for i in range(n_samples)]  # Mock 입력 데이터
    true_velocities = np.random.uniform(50, 130, n_samples)  # 실제 velocity 값들 (cm/s)
    
    print(f"\nTesting with {n_samples} samples...")
    
    # 배치 분석 수행
    results_df = integrated_system.batch_analysis(input_data_list, true_velocities.tolist())
    
    # 결과 출력
    print("\nSample Results (first 3 samples):")
    display_cols = ['sample_id', 'original_Velocity', 'calibrated_Velocity', 
                   'measured_velocity', 'calibration_applied']
    available_cols = [col for col in display_cols if col in results_df.columns]
    print(results_df[available_cols].head(3))
    
    # 보정 효과 평가
    if integrated_system.calibration_available:
        evaluation = integrated_system.evaluate_calibration_impact(
            input_data_list, true_velocities.tolist()
        )
        
        print(f"\n=== Calibration Impact Evaluation ===")
        print(f"Original Velocity MAE: {evaluation['original_velocity_mae']:.4f}")
        print(f"Calibrated Velocity MAE: {evaluation['calibrated_velocity_mae']:.4f}")
        print(f"Velocity Improvement: {evaluation['velocity_improvement']:.4f}")
        
        # 시각화
        plt.figure(figsize=(12, 5))
        
        # Velocity 오차 비교
        plt.subplot(1, 2, 1)
        original_errors = np.abs(results_df['original_Velocity'] - results_df['measured_velocity'])
        calibrated_errors = np.abs(results_df['calibrated_Velocity'] - results_df['measured_velocity'])
        
        x = np.arange(len(original_errors))
        plt.bar(x - 0.2, original_errors, 0.4, label='Original', alpha=0.7)
        plt.bar(x + 0.2, calibrated_errors, 0.4, label='Calibrated', alpha=0.7)
        plt.xlabel('Sample')
        plt.ylabel('Velocity Error (cm/s)')
        plt.title('Velocity Prediction Errors')
        plt.legend()

        # 전체 오차 분포
        plt.subplot(1, 2, 2)
        plt.hist(original_errors, alpha=0.7, label='Original', bins=10)
        plt.hist(calibrated_errors, alpha=0.7, label='Calibrated', bins=10)
        plt.xlabel('Velocity Error (cm/s)')
        plt.ylabel('Frequency')
        plt.title('Error Distribution')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('calibration_impact_demo.png', dpi=300, bbox_inches='tight')
        plt.show()
    
    return integrated_system, results_df

if __name__ == "__main__":
    # 데모 실행
    try:
        system, results = demo_integration()
        print("\nDemo completed successfully!")
        print("Check 'calibration_impact_demo.png' for visualization results.")
    except Exception as e:
        print(f"Demo failed: {e}")
        print("Make sure to train the calibration models first:")
        print("python run_calibration.py full")

#!/usr/bin/env python3
"""
Gait Parameter Calibration 실행 스크립트
"""

from gait_automl_calibration import GaitParameterCalibrator
import numpy as np
import pandas as pd

def quick_demo(remove_outliers=True):
    """
    빠른 데모 실행 (시간 단축)

    Args:
        remove_outliers (bool): 아웃라이어 제거 여부
    """
    print("=== Quick Demo: Gait Parameter Calibration ===")

    # 짧은 학습 시간으로 데모 실행
    calibrator = GaitParameterCalibrator(automl_time_budget=60)  # 1분

    # 데이터 로드
    df = calibrator.load_data()

    # 샘플 데이터로 축소 (빠른 실행을 위해)
    calibrator.df = calibrator.df.sample(n=min(1000, len(calibrator.df)), random_state=42)
    print(f"Using {len(calibrator.df)} samples for quick demo")

    # 아웃라이어 제거
    if remove_outliers:
        df = calibrator.remove_outliers(method='iqr', visualize=True)
    else:
        print("Skipping outlier removal for quick demo...")

    # 데이터 탐색
    correlations = calibrator.explore_data()

    # 데이터 준비
    calibrator.prepare_data()

    # AutoML 모델 학습
    calibrator.train_automl_models()

    # 모델 평가
    results_df = calibrator.evaluate_models()

    # 예측 결과 시각화
    calibrator.visualize_predictions()

    return calibrator, results_df

def full_training(outlier_method='combined', remove_outliers=True):
    """
    전체 데이터로 완전한 학습 실행

    Args:
        outlier_method (str): 아웃라이어 제거 방법
        remove_outliers (bool): 아웃라이어 제거 여부
    """
    print("=== Full Training: Gait Parameter Calibration ===")

    # 충분한 학습 시간 설정
    calibrator = GaitParameterCalibrator(automl_time_budget=1800)  # 30분

    # 전체 파이프라인 실행
    calibrator.load_data()

    # 아웃라이어 제거
    if remove_outliers:
        calibrator.remove_outliers(method=outlier_method, visualize=True)
    else:
        print("Skipping outlier removal...")

    calibrator.explore_data()
    calibrator.prepare_data()
    calibrator.train_automl_models()
    results_df = calibrator.evaluate_models()
    calibrator.visualize_predictions()
    calibrator.save_models()

    return calibrator, results_df

def load_and_predict():
    """
    저장된 멀티 아웃풋 모델을 로드하여 예측 수행
    """
    from calibration_utils import GaitCalibrationPredictor

    print("=== Loading Saved Multi-Output Model for Prediction ===")

    try:
        # 멀티 아웃풋 예측기 초기화
        predictor = GaitCalibrationPredictor()

        # 예시 예측 (cm/s 단위)
        example_velocities = [50, 70, 90, 110, 130]  # cm/s

        print("\n=== Multi-Output Prediction Results ===")
        print("Velocity (cm/s) -> Predicted Gait Parameters")
        print("-" * 60)

        for vel in example_velocities:
            print(f"\nVelocity: {vel} cm/s")
            predictions = predictor.predict_single(vel)

            for param, value in predictions.items():
                print(f"  {param:15s}: {value:8.3f}")

        return predictor

    except Exception as e:
        print(f"Failed to load models: {e}")
        print("Please run training first:")
        print("python run_calibration.py full")
        return None

def analyze_velocity_sensitivity():
    """
    Velocity 변화에 따른 각 parameter의 민감도 분석 (멀티 아웃풋 모델)
    """
    print("=== Velocity Sensitivity Analysis (Multi-Output) ===")

    # 저장된 멀티 아웃풋 모델 로드
    predictor = load_and_predict()
    if predictor is None:
        return

    # Velocity 범위 설정 (cm/s 단위)
    velocity_range = np.linspace(30, 150, 50)

    # 배치 예측으로 모든 parameter 값 계산
    batch_predictions = predictor.predict_batch(velocity_range.tolist())

    # 시각화
    import matplotlib.pyplot as plt

    plt.figure(figsize=(15, 12))

    for i, target in enumerate(predictor.target_columns):
        plt.subplot(3, 4, i+1)
        pred_values = batch_predictions[target].values
        plt.plot(velocity_range, pred_values, 'b-', linewidth=2)
        plt.xlabel('Velocity (cm/s)')
        plt.ylabel(target)
        plt.title(f'{target} vs Velocity')
        plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('velocity_sensitivity_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 민감도 계산 (velocity 변화에 대한 parameter 변화율)
    print("\n=== Sensitivity Coefficients ===")
    print("Parameter               Sensitivity (Δparam/Δvelocity)")
    print("-" * 55)

    for target in predictor.target_columns:
        pred_values = batch_predictions[target].values
        # 선형 회귀로 기울기 계산
        from sklearn.linear_model import LinearRegression
        reg = LinearRegression().fit(velocity_range.reshape(-1, 1), pred_values)
        sensitivity = reg.coef_[0]
        print(f"{target:20s}: {sensitivity:10.4f}")

def create_prediction_scatter_plots():
    """
    다양한 velocity 값들에 대한 예측값 scatter plot 생성
    """
    print("=== Creating Prediction Scatter Plots ===")

    # 저장된 멀티 아웃풋 모델 로드
    predictor = load_and_predict()
    if predictor is None:
        return

    # 다양한 velocity 값들 생성 (실제 데이터 범위 기반)
    np.random.seed(42)  # 재현 가능한 결과를 위해
    n_points = 200
    velocity_values = np.random.uniform(30, 150, n_points)  # cm/s

    # 배치 예측
    batch_predictions = predictor.predict_batch(velocity_values.tolist())

    import matplotlib.pyplot as plt

    # 1. 전체 scatter plot (모든 parameters)
    plt.figure(figsize=(20, 16))

    for i, target in enumerate(predictor.target_columns):
        plt.subplot(3, 4, i+1)
        pred_values = batch_predictions[target].values

        # Velocity에 따른 색상 그라디언트
        scatter = plt.scatter(velocity_values, pred_values,
                            c=velocity_values, cmap='viridis',
                            alpha=0.7, s=30)

        plt.xlabel('Velocity (cm/s)')
        plt.ylabel(f'{target}')
        plt.title(f'{target} Predictions')
        plt.grid(True, alpha=0.3)

        # 컬러바 추가
        plt.colorbar(scatter, label='Velocity (cm/s)')

    plt.tight_layout()
    plt.savefig('prediction_scatter_plots_all.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 주요 parameters만 선별한 큰 scatter plot
    key_params = ['Stride_Len_L', 'Stride_Len_R', 'D_Supp_PercL', 'D_Supp_PercR']

    plt.figure(figsize=(12, 10))

    for i, target in enumerate(key_params):
        plt.subplot(2, 2, i+1)
        pred_values = batch_predictions[target].values

        # 더 큰 점과 더 선명한 색상
        scatter = plt.scatter(velocity_values, pred_values,
                            c=velocity_values, cmap='plasma',
                            alpha=0.8, s=50, edgecolors='black', linewidth=0.5)

        plt.xlabel('Velocity (cm/s)', fontsize=12)
        plt.ylabel(f'{target}', fontsize=12)
        plt.title(f'{target} vs Velocity', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)

        # 트렌드 라인 추가
        from sklearn.linear_model import LinearRegression
        reg = LinearRegression().fit(velocity_values.reshape(-1, 1), pred_values)
        trend_line = reg.predict(velocity_values.reshape(-1, 1))
        plt.plot(velocity_values, trend_line, 'r--', alpha=0.8, linewidth=2, label='Trend')

        plt.legend()
        plt.colorbar(scatter, label='Velocity (cm/s)')

    plt.tight_layout()
    plt.savefig('prediction_scatter_plots_key.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 3D scatter plot (Velocity vs 두 주요 parameters)
    from mpl_toolkits.mplot3d import Axes3D

    fig = plt.figure(figsize=(15, 5))

    # 3D plot 1: Velocity vs Stride Length (L&R)
    ax1 = fig.add_subplot(131, projection='3d')
    stride_len_l = batch_predictions['Stride_Len_L'].values
    stride_len_r = batch_predictions['Stride_Len_R'].values

    scatter1 = ax1.scatter(velocity_values, stride_len_l, stride_len_r,
                          c=velocity_values, cmap='viridis', s=30, alpha=0.7)
    ax1.set_xlabel('Velocity (cm/s)')
    ax1.set_ylabel('Stride_Len_L')
    ax1.set_zlabel('Stride_Len_R')
    ax1.set_title('3D: Velocity vs Stride Lengths')

    # 3D plot 2: Velocity vs Double Support (L&R)
    ax2 = fig.add_subplot(132, projection='3d')
    d_supp_l = batch_predictions['D_Supp_PercL'].values
    d_supp_r = batch_predictions['D_Supp_PercR'].values

    scatter2 = ax2.scatter(velocity_values, d_supp_l, d_supp_r,
                          c=velocity_values, cmap='plasma', s=30, alpha=0.7)
    ax2.set_xlabel('Velocity (cm/s)')
    ax2.set_ylabel('D_Supp_PercL')
    ax2.set_zlabel('D_Supp_PercR')
    ax2.set_title('3D: Velocity vs Double Support')

    # 3D plot 3: Velocity vs Swing Percentage (L&R)
    ax3 = fig.add_subplot(133, projection='3d')
    swing_l = batch_predictions['Swing_Perc_L'].values
    swing_r = batch_predictions['Swing_Perc_R'].values

    scatter3 = ax3.scatter(velocity_values, swing_l, swing_r,
                          c=velocity_values, cmap='coolwarm', s=30, alpha=0.7)
    ax3.set_xlabel('Velocity (cm/s)')
    ax3.set_ylabel('Swing_Perc_L')
    ax3.set_zlabel('Swing_Perc_R')
    ax3.set_title('3D: Velocity vs Swing Percentages')

    plt.tight_layout()
    plt.savefig('prediction_scatter_plots_3d.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 4. 통계 정보 출력
    print("\n=== Prediction Statistics ===")
    print(f"Number of prediction points: {n_points}")
    print(f"Velocity range: {velocity_values.min():.1f} - {velocity_values.max():.1f} cm/s")
    print("\nPrediction ranges for each parameter:")
    for target in predictor.target_columns:
        pred_values = batch_predictions[target].values
        print(f"  {target:15s}: {pred_values.min():8.3f} - {pred_values.max():8.3f}")

    print("\nFiles saved:")
    print("  - prediction_scatter_plots_all.png (모든 parameters)")
    print("  - prediction_scatter_plots_key.png (주요 parameters)")
    print("  - prediction_scatter_plots_3d.png (3D plots)")

    return batch_predictions

def create_correlation_analysis():
    """
    예측된 gait parameters 간의 상관관계 분석
    """
    print("=== Creating Correlation Analysis ===")

    # 저장된 멀티 아웃풋 모델 로드
    from calibration_utils import GaitCalibrationPredictor, create_correlation_heatmap

    try:
        predictor = GaitCalibrationPredictor()

        # 상관관계 히트맵 생성
        correlation_matrix = create_correlation_heatmap(predictor, n_points=300)

        return correlation_matrix

    except Exception as e:
        print(f"Failed to create correlation analysis: {e}")
        print("Please make sure models are trained first:")
        print("python run_calibration.py full")
        return None

def validate_predictions_vs_groundtruth():
    """
    Ground-truth와 예측값 간의 correlation 시각화 및 분석
    """
    print("=== Ground-Truth vs Predictions Validation ===")

    from gait_automl_calibration import GaitParameterCalibrator
    import matplotlib.pyplot as plt
    import seaborn as sns
    from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
    import numpy as np

    # 캘리브레이터 초기화 및 데이터 로드
    calibrator = GaitParameterCalibrator(automl_time_budget=60)
    df = calibrator.load_data()

    # 샘플 데이터로 축소 (빠른 실행을 위해)
    df_sample = df.sample(n=min(2000, len(df)), random_state=42)
    calibrator.df = df_sample
    print(f"Using {len(df_sample)} samples for validation")

    # 데이터 준비
    calibrator.prepare_data()

    # 저장된 모델 로드
    try:
        from calibration_utils import GaitCalibrationPredictor
        predictor = GaitCalibrationPredictor()

        # 테스트 데이터에서 예측 수행
        X_test = calibrator.train_test_splits[calibrator.target_columns[0]]['X_test']
        velocity_scaler = calibrator.train_test_splits[calibrator.target_columns[0]]['scaler_X']

        # 원본 velocity 값 복원 (스케일링 해제)
        X_test_original = velocity_scaler.inverse_transform(X_test)
        velocity_values = X_test_original.flatten()

        # 배치 예측
        predictions_df = predictor.predict_batch(velocity_values.tolist())

        # Ground-truth 값들 수집
        ground_truth = {}
        for target in calibrator.target_columns:
            ground_truth[target] = calibrator.train_test_splits[target]['y_test'].values

        # 1. 전체 correlation scatter plot
        plt.figure(figsize=(20, 16))

        correlations = {}
        r2_scores = {}

        for i, target in enumerate(calibrator.target_columns):
            plt.subplot(3, 4, i+1)

            y_true = ground_truth[target]
            y_pred = predictions_df[target].values

            # Correlation 계산
            correlation = np.corrcoef(y_true, y_pred)[0, 1]
            r2 = r2_score(y_true, y_pred)
            mse = mean_squared_error(y_true, y_pred)
            mae = mean_absolute_error(y_true, y_pred)

            correlations[target] = correlation
            r2_scores[target] = r2

            # Scatter plot
            plt.scatter(y_true, y_pred, alpha=0.6, s=20, c='blue', edgecolors='none')

            # Perfect prediction line (y=x)
            min_val = min(y_true.min(), y_pred.min())
            max_val = max(y_true.max(), y_pred.max())
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2)

            # 회귀선 추가
            from sklearn.linear_model import LinearRegression
            reg = LinearRegression().fit(y_true.reshape(-1, 1), y_pred)
            reg_line = reg.predict(np.array([min_val, max_val]).reshape(-1, 1))
            plt.plot([min_val, max_val], reg_line, 'g-', alpha=0.7, linewidth=1.5)

            plt.xlabel('Ground Truth')
            plt.ylabel('Predicted')
            plt.title(f'{target}\\nr={correlation:.3f}, R²={r2:.3f}\\nMAE={mae:.3f}')
            plt.grid(True, alpha=0.3)

            # 1:1 비율 유지
            plt.axis('equal')
            plt.xlim(min_val, max_val)
            plt.ylim(min_val, max_val)

        plt.tight_layout()
        plt.savefig('groundtruth_vs_predictions_all.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 2. 주요 parameters 상세 분석
        key_params = ['Stride_Len_L', 'Stride_Len_R', 'D_Supp_PercL', 'D_Supp_PercR']

        plt.figure(figsize=(15, 12))

        for i, target in enumerate(key_params):
            plt.subplot(2, 2, i+1)

            y_true = ground_truth[target]
            y_pred = predictions_df[target].values

            correlation = correlations[target]
            r2 = r2_scores[target]

            # 더 상세한 scatter plot
            plt.scatter(y_true, y_pred, alpha=0.7, s=30, c=velocity_values,
                       cmap='viridis', edgecolors='black', linewidth=0.5)

            # Perfect prediction line
            min_val = min(y_true.min(), y_pred.min())
            max_val = max(y_true.max(), y_pred.max())
            plt.plot([min_val, max_val], [min_val, max_val], 'r--', alpha=0.8, linewidth=2, label='Perfect')

            # 회귀선
            reg = LinearRegression().fit(y_true.reshape(-1, 1), y_pred)
            reg_line = reg.predict(np.array([min_val, max_val]).reshape(-1, 1))
            plt.plot([min_val, max_val], reg_line, 'orange', alpha=0.8, linewidth=2, label='Regression')

            plt.xlabel('Ground Truth', fontsize=12)
            plt.ylabel('Predicted', fontsize=12)
            plt.title(f'{target}\\nCorrelation: {correlation:.4f}, R²: {r2:.4f}',
                     fontsize=14, fontweight='bold')
            plt.grid(True, alpha=0.3)
            plt.legend()

            # 컬러바 추가
            cbar = plt.colorbar(plt.cm.ScalarMappable(cmap='viridis'), ax=plt.gca())
            cbar.set_label('Velocity (cm/s)', rotation=270, labelpad=15)

            plt.axis('equal')
            plt.xlim(min_val, max_val)
            plt.ylim(min_val, max_val)

        plt.tight_layout()
        plt.savefig('groundtruth_vs_predictions_detailed.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 3. Correlation 및 R² 요약 바 차트
        plt.figure(figsize=(15, 10))

        # Correlation 바 차트
        plt.subplot(2, 1, 1)
        params = list(correlations.keys())
        corr_values = list(correlations.values())

        colors = ['green' if x > 0.8 else 'orange' if x > 0.6 else 'red' for x in corr_values]
        bars1 = plt.bar(range(len(params)), corr_values, color=colors, alpha=0.7, edgecolor='black')

        plt.xlabel('Parameters')
        plt.ylabel('Correlation (r)')
        plt.title('Correlation between Ground Truth and Predictions', fontsize=14, fontweight='bold')
        plt.xticks(range(len(params)), params, rotation=45, ha='right')
        plt.ylim(0, 1)
        plt.grid(True, alpha=0.3, axis='y')

        # 값 표시
        for i, (bar, val) in enumerate(zip(bars1, corr_values)):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{val:.3f}', ha='center', va='bottom', fontweight='bold')

        # R² 바 차트
        plt.subplot(2, 1, 2)
        r2_values = list(r2_scores.values())

        colors = ['green' if x > 0.7 else 'orange' if x > 0.5 else 'red' for x in r2_values]
        bars2 = plt.bar(range(len(params)), r2_values, color=colors, alpha=0.7, edgecolor='black')

        plt.xlabel('Parameters')
        plt.ylabel('R² Score')
        plt.title('R² Score between Ground Truth and Predictions', fontsize=14, fontweight='bold')
        plt.xticks(range(len(params)), params, rotation=45, ha='right')
        plt.ylim(0, 1)
        plt.grid(True, alpha=0.3, axis='y')

        # 값 표시
        for i, (bar, val) in enumerate(zip(bars2, r2_values)):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                    f'{val:.3f}', ha='center', va='bottom', fontweight='bold')

        plt.tight_layout()
        plt.savefig('correlation_r2_summary.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 4. Bland-Altman Plot (잔차 분석)
        plt.figure(figsize=(20, 16))

        for i, target in enumerate(calibrator.target_columns):
            plt.subplot(3, 4, i+1)

            y_true = ground_truth[target]
            y_pred = predictions_df[target].values

            # Bland-Altman plot 계산
            mean_values = (y_true + y_pred) / 2  # 평균값 (x축)
            differences = y_true - y_pred        # 차이값 (y축)

            # 통계 계산
            mean_diff = np.mean(differences)     # 평균 residual
            std_diff = np.std(differences)       # 표준편차

            # 95% 신뢰구간 (±1.96 * std)
            upper_limit = mean_diff + 1.96 * std_diff
            lower_limit = mean_diff - 1.96 * std_diff

            # Scatter plot
            plt.scatter(mean_values, differences, alpha=0.6, s=20, c='purple', edgecolors='none')

            # Zero line (perfect agreement)
            plt.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=2, label='Zero line')

            # 평균 residual line
            plt.axhline(y=mean_diff, color='red', linestyle='--', alpha=0.8, linewidth=2,
                       label=f'Mean bias: {mean_diff:.3f}')

            # 95% 신뢰구간 lines
            plt.axhline(y=upper_limit, color='orange', linestyle=':', alpha=0.8, linewidth=1.5,
                       label=f'95% limits: ±{1.96*std_diff:.3f}')
            plt.axhline(y=lower_limit, color='orange', linestyle=':', alpha=0.8, linewidth=1.5)

            plt.xlabel('Mean of True and Predicted')
            plt.ylabel('Difference (True - Predicted)')
            plt.title(f'{target} Bland-Altman Plot\\nMean bias: {mean_diff:.3f}, Std: {std_diff:.3f}')
            plt.grid(True, alpha=0.3)
            plt.legend(fontsize=8)

            # 평균 residual 값을 텍스트로 표시
            plt.text(0.05, 0.95, f'Mean residual: {mean_diff:.4f}',
                    transform=plt.gca().transAxes, fontsize=10, fontweight='bold',
                    bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

        plt.tight_layout()
        plt.savefig('bland_altman_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

        # 5. 통계 요약 출력
        print("\\n" + "="*80)
        print("GROUND-TRUTH vs PREDICTIONS VALIDATION SUMMARY")
        print("="*80)

        print(f"\\n📊 Dataset Info:")
        print(f"   Samples used: {len(velocity_values)}")
        print(f"   Velocity range: {velocity_values.min():.1f} - {velocity_values.max():.1f} cm/s")

        print(f"\\n🎯 Overall Performance:")
        avg_correlation = np.mean(list(correlations.values()))
        avg_r2 = np.mean(list(r2_scores.values()))
        print(f"   Average Correlation: {avg_correlation:.4f}")
        print(f"   Average R² Score: {avg_r2:.4f}")

        print(f"\\n🏆 Best Performing Parameters (Correlation > 0.8):")
        best_params = [(k, v) for k, v in correlations.items() if v > 0.8]
        best_params.sort(key=lambda x: x[1], reverse=True)
        for param, corr in best_params:
            r2 = r2_scores[param]
            print(f"   {param:15s}: r={corr:.4f}, R²={r2:.4f}")

        print(f"\\n⚠️  Parameters needing improvement (Correlation < 0.6):")
        poor_params = [(k, v) for k, v in correlations.items() if v < 0.6]
        poor_params.sort(key=lambda x: x[1])
        for param, corr in poor_params:
            r2 = r2_scores[param]
            print(f"   {param:15s}: r={corr:.4f}, R²={r2:.4f}")

        print(f"\\n📁 Generated Files:")
        print(f"   - groundtruth_vs_predictions_all.png (전체 parameters)")
        print(f"   - groundtruth_vs_predictions_detailed.png (주요 parameters)")
        print(f"   - correlation_r2_summary.png (성능 요약)")
        print(f"   - bland_altman_analysis.png (Bland-Altman plot 분석)")

        return correlations, r2_scores, ground_truth, predictions_df

    except Exception as e:
        print(f"Validation failed: {e}")
        print("Please make sure models are trained first:")
        print("python run_calibration.py full")
        return None

def analyze_outliers():
    """
    아웃라이어 분석 전용 함수
    """
    print("=== Outlier Analysis ===")

    from gait_automl_calibration import GaitParameterCalibrator

    # 캘리브레이터 초기화
    calibrator = GaitParameterCalibrator()

    # 데이터 로드
    calibrator.load_data()

    print(f"\n📊 Original data shape: {calibrator.df.shape}")

    # 다양한 방법으로 아웃라이어 분석
    methods = ['iqr', 'z_score', 'isolation_forest', 'combined']

    results = {}

    for method in methods:
        print(f"\n{'='*50}")
        print(f"Testing {method.upper()} method")
        print(f"{'='*50}")

        # 데이터 복사본으로 테스트
        test_calibrator = GaitParameterCalibrator()
        test_calibrator.df = calibrator.df.copy()

        # 아웃라이어 제거
        cleaned_df = test_calibrator.remove_outliers(method=method, visualize=False)

        results[method] = {
            'original_count': len(calibrator.df),
            'final_count': len(cleaned_df),
            'removed_count': len(calibrator.df) - len(cleaned_df),
            'removal_percentage': ((len(calibrator.df) - len(cleaned_df)) / len(calibrator.df)) * 100
        }

    # 결과 요약
    print(f"\n{'='*80}")
    print("OUTLIER REMOVAL COMPARISON")
    print(f"{'='*80}")

    print(f"{'Method':<20} {'Original':<10} {'Final':<10} {'Removed':<10} {'Percentage':<12}")
    print("-" * 70)

    for method, result in results.items():
        print(f"{method:<20} {result['original_count']:<10} {result['final_count']:<10} "
              f"{result['removed_count']:<10} {result['removal_percentage']:<12.2f}%")

    # 권장사항
    print(f"\n💡 Recommendations:")

    # 가장 보수적인 방법 (가장 적게 제거)
    conservative = min(results.items(), key=lambda x: x[1]['removal_percentage'])
    print(f"   Most conservative: {conservative[0]} ({conservative[1]['removal_percentage']:.2f}% removed)")

    # 가장 적극적인 방법 (가장 많이 제거)
    aggressive = max(results.items(), key=lambda x: x[1]['removal_percentage'])
    print(f"   Most aggressive: {aggressive[0]} ({aggressive[1]['removal_percentage']:.2f}% removed)")

    # 권장 방법 (5-15% 제거 범위)
    recommended = []
    for method, result in results.items():
        if 5 <= result['removal_percentage'] <= 15:
            recommended.append((method, result['removal_percentage']))

    if recommended:
        print(f"   Recommended (5-15% removal): {', '.join([f'{m} ({p:.1f}%)' for m, p in recommended])}")
    else:
        print(f"   Recommended: Use 'iqr' method as a balanced approach")

    return results

def preprocess_data():
    """
    데이터 전처리 및 저장 전용 함수
    """
    from gait_automl_calibration import preprocess_and_save_data

    print("=== Data Preprocessing Mode ===")
    print("Available outlier removal methods:")
    print("  1. iqr (recommended)")
    print("  2. z_score (conservative)")
    print("  3. isolation_forest (ML-based)")
    print("  4. combined (aggressive)")

    method = input("Choose method (iqr/z_score/isolation_forest/combined) [default: iqr]: ").strip().lower()
    if not method:
        method = 'iqr'

    if method not in ['iqr', 'z_score', 'isolation_forest', 'combined']:
        print("Invalid method. Using 'iqr' as default.")
        method = 'iqr'

    # 전처리 실행
    saved_path = preprocess_and_save_data(outlier_method=method, visualize=True)

    print(f"\n🎉 Preprocessing completed!")
    print(f"📁 Data saved to: {saved_path}")
    print(f"🚀 You can now use 'train_preprocessed' mode to train models")

    return saved_path

def train_preprocessed():
    """
    전처리된 데이터로 모델 학습
    """
    from gait_automl_calibration import train_from_saved_data
    import os

    print("=== Training from Preprocessed Data ===")

    # 사용 가능한 전처리 데이터 파일 찾기
    data_dir = './data'
    if os.path.exists(data_dir):
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv') and 'preprocessed' in f]

        if csv_files:
            print("Available preprocessed data files:")
            for i, file in enumerate(csv_files, 1):
                print(f"  {i}. {file}")

            try:
                choice = input(f"Choose file (1-{len(csv_files)}) [default: 1]: ").strip()
                if not choice:
                    choice = '1'

                file_idx = int(choice) - 1
                if 0 <= file_idx < len(csv_files):
                    selected_file = csv_files[file_idx]
                    data_path = os.path.join(data_dir, selected_file)
                else:
                    print("Invalid choice. Using first file.")
                    data_path = os.path.join(data_dir, csv_files[0])
            except ValueError:
                print("Invalid input. Using first file.")
                data_path = os.path.join(data_dir, csv_files[0])
        else:
            print("No preprocessed data files found.")
            print("Please run 'preprocess' mode first.")
            return None
    else:
        print("Data directory not found.")
        print("Please run 'preprocess' mode first.")
        return None

    print(f"Using data file: {data_path}")

    # 학습 실행
    calibrator = train_from_saved_data(data_path=data_path, automl_time_budget=600)

    return calibrator

def train_ensemble():
    """
    앙상블 모델 학습
    """
    from gait_automl_calibration import train_from_saved_data
    import os

    print("=== Ensemble Model Training ===")

    # 사용 가능한 전처리 데이터 파일 찾기
    data_dir = './data'
    if os.path.exists(data_dir):
        csv_files = [f for f in os.listdir(data_dir) if f.endswith('.csv') and 'preprocessed' in f]

        if csv_files:
            print("Available preprocessed data files:")
            for i, file in enumerate(csv_files, 1):
                print(f"  {i}. {file}")

            try:
                choice = input(f"Choose file (1-{len(csv_files)}) [default: 1]: ").strip()
                if not choice:
                    choice = '1'

                file_idx = int(choice) - 1
                if 0 <= file_idx < len(csv_files):
                    selected_file = csv_files[file_idx]
                    data_path = os.path.join(data_dir, selected_file)
                else:
                    print("Invalid choice. Using first file.")
                    data_path = os.path.join(data_dir, csv_files[0])
            except ValueError:
                print("Invalid input. Using first file.")
                data_path = os.path.join(data_dir, csv_files[0])
        else:
            print("No preprocessed data files found.")
            print("Please run 'preprocess' mode first.")
            return None
    else:
        print("Data directory not found.")
        print("Please run 'preprocess' mode first.")
        return None

    print(f"Using data file: {data_path}")

    # 앙상블 설정
    print("\nEnsemble Configuration:")
    print("Available ensemble methods:")
    print("  1. voting (평균 기반)")
    print("  2. stacking (메타 모델 기반)")
    print("  3. blending (홀드아웃 기반)")

    method_choice = input("Choose ensemble method (1-3) [default: 2]: ").strip()
    method_map = {'1': 'voting', '2': 'stacking', '3': 'blending'}
    ensemble_method = method_map.get(method_choice, 'stacking')

    n_models = input("Number of models to ensemble (3-8) [default: 5]: ").strip()
    try:
        n_models = int(n_models)
        if not 3 <= n_models <= 8:
            n_models = 5
    except ValueError:
        n_models = 5

    print(f"Training {ensemble_method} ensemble with {n_models} models...")

    # 앙상블 학습 실행
    from gait_automl_calibration import PreprocessedGaitCalibrator

    calibrator = PreprocessedGaitCalibrator(
        preprocessed_data_path=data_path,
        automl_time_budget=1800  # 30분
    )

    # 데이터 로드 및 준비
    calibrator.load_data()
    calibrator.prepare_data()

    # 앙상블 모델 학습
    calibrator.train_ensemble_models(n_models=n_models, ensemble_method=ensemble_method)

    # 앙상블 모델 평가
    results_df = calibrator.evaluate_ensemble_models()

    # 모델 저장
    calibrator.save_models()

    print(f"\n🎉 Ensemble training completed!")
    print(f"📊 Method: {ensemble_method}")
    print(f"🤖 Models: {n_models}")

    return calibrator

def validate_preprocessed():
    """
    전처리된 데이터로 학습한 모델의 검증
    """
    print("=== Validation with Preprocessed Data ===")

    # 저장된 모델로 검증 실행
    try:
        validate_predictions_vs_groundtruth()
    except Exception as e:
        print(f"Validation failed: {e}")
        print("Please make sure models are trained with preprocessed data first:")
        print("python run_calibration.py train_preprocessed")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    else:
        print("Usage: python run_calibration.py [mode]")
        print("\n📊 Data Preprocessing:")
        print("  preprocess      : Preprocess data and save to ./data/")
        print("  train_preprocessed : Train models using preprocessed data")
        print("  ensemble        : Train ensemble models for maximum performance")
        print("\n🚀 Original Modes:")
        print("  demo            : Quick demo with reduced data")
        print("  full            : Full training with all data")
        print("\n📈 Analysis & Visualization:")
        print("  predict         : Load saved models and make predictions")
        print("  sensitivity     : Analyze velocity sensitivity")
        print("  scatter         : Create prediction scatter plots")
        print("  correlation     : Create correlation analysis")
        print("  validate        : Validate predictions vs ground-truth")
        print("  outliers        : Analyze outliers with different methods")

        mode = input("\nEnter mode: ").lower()

    if mode == 'preprocess':
        preprocess_data()
    elif mode == 'train_preprocessed':
        train_preprocessed()
    elif mode == 'ensemble':
        train_ensemble()
    elif mode == 'demo':
        calibrator, results = quick_demo()
    elif mode == 'full':
        calibrator, results = full_training()
    elif mode == 'predict':
        load_and_predict()
    elif mode == 'sensitivity':
        analyze_velocity_sensitivity()
    elif mode == 'scatter':
        create_prediction_scatter_plots()
    elif mode == 'correlation':
        create_correlation_analysis()
    elif mode == 'validate':
        validate_predictions_vs_groundtruth()
    elif mode == 'outliers':
        analyze_outliers()
    else:
        print("Invalid mode. Please choose from the available options.")

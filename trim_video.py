from pathlib import Path
import cv2
import math

def trim_center_segment(input_path: str,
                        output_path: str,
                        original_distance: float = 4.8,
                        target_distance: float = 3.0):
    """
    원본 비디오가 original_distance(m)의 보행 구간을 담고 있다고 가정하고,
    그 가운데 target_distance(m) 만큼을 비례식으로 잘라내 저장합니다.

    Params:
    - input_path (str): 입력 비디오 경로
    - output_path (str): 잘라낸 비디오 저장 경로
    - original_distance (float): 입력 비디오가 커버하는 총 보행 거리 (m), 기본 4.8
    - target_distance (float): 잘라낼 보행 거리 (m), 기본 3.0
    """
    # 파일 경로 처리
    input_path = Path(input_path)
    output_path = input_path.with_stem("proc")
    
    # 비디오 로드
    cap = cv2.VideoCapture(str(input_path))
    if not cap.isOpened():
        raise IOError(f"Cannot open video: {input_path}")

    fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    
    width  = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    # 중앙 segment 계산 (프레임 단위)
    segment_ratio  = target_distance / original_distance
    segment_frames = int(math.floor(total_frames * segment_ratio))
    start_frame    = int(math.floor((total_frames - segment_frames) / 2))

    # 출력 비디오 세팅
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))

    # 시작 프레임으로 위치 이동
    cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)

    # segment_frames 개수만큼 프레임 기록
    for _ in range(segment_frames):
        ret, frame = cap.read()
        if not ret:
            break
        out.write(frame)

    cap.release()
    out.release()

    print(f"[Info] Trimmed center segment saved to {output_path}, "
          f"frames {start_frame}–{start_frame + segment_frames - 1}")

if __name__ == "__main__":
    # Example usage
    trim_center_segment('./output.mp4', "output_3m_center.mp4", target_distance=3.0)
